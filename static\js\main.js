// إدارة الوضع الداكن
class ThemeManager {
    constructor() {
        this.theme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.applyTheme();
        this.createToggleButton();
        this.bindEvents();
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        this.updateToggleIcon();
    }

    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        localStorage.setItem('theme', this.theme);
        this.applyTheme();
    }

    createToggleButton() {
        const toggleButton = document.createElement('button');
        toggleButton.className = 'theme-toggle';
        toggleButton.id = 'themeToggle';
        toggleButton.innerHTML = '<i class="fas fa-moon"></i>';
        toggleButton.title = 'تبديل الوضع الداكن';
        document.body.appendChild(toggleButton);
    }

    updateToggleIcon() {
        const toggleButton = document.getElementById('themeToggle');
        if (toggleButton) {
            const icon = this.theme === 'light' ? 'fa-moon' : 'fa-sun';
            toggleButton.innerHTML = `<i class="fas ${icon}"></i>`;
        }
    }

    bindEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('#themeToggle')) {
                this.toggleTheme();
            }
        });
    }
}

// إدارة الشريط الجانبي
class SidebarManager {
    constructor() {
        this.sidebar = document.querySelector('.sidebar');
        this.toggleButton = document.querySelector('.sidebar-toggle');
        this.init();
    }

    init() {
        this.bindEvents();
        this.setActiveLink();
    }

    toggle() {
        if (this.sidebar) {
            this.sidebar.classList.toggle('show');
        }
    }

    setActiveLink() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.sidebar .nav-link');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });
    }

    bindEvents() {
        if (this.toggleButton) {
            this.toggleButton.addEventListener('click', () => this.toggle());
        }

        // إغلاق الشريط الجانبي عند النقر خارجه في الشاشات الصغيرة
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768 && this.sidebar && this.sidebar.classList.contains('show')) {
                if (!this.sidebar.contains(e.target) && !e.target.closest('.sidebar-toggle')) {
                    this.sidebar.classList.remove('show');
                }
            }
        });
    }
}

// إدارة النماذج
class FormManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindFormEvents();
        this.setupValidation();
    }

    bindFormEvents() {
        // تأكيد الحذف
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('delete-btn')) {
                e.preventDefault();
                this.confirmDelete(e.target);
            }
        });

        // إرسال النماذج بـ AJAX
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('ajax-form')) {
                e.preventDefault();
                this.submitForm(e.target);
            }
        });
    }

    confirmDelete(button) {
        const itemName = button.getAttribute('data-item-name') || 'هذا العنصر';
        
        if (confirm(`هل أنت متأكد من حذف ${itemName}؟`)) {
            const form = button.closest('form');
            if (form) {
                form.submit();
            } else {
                window.location.href = button.getAttribute('href');
            }
        }
    }

    async submitForm(form) {
        const formData = new FormData(form);
        const submitButton = form.querySelector('button[type="submit"]');
        
        // إظهار حالة التحميل
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="loading"></span> جاري الحفظ...';
        }

        try {
            const response = await fetch(form.action, {
                method: form.method,
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('تم الحفظ بنجاح!', 'success');
                if (result.redirect) {
                    setTimeout(() => {
                        window.location.href = result.redirect;
                    }, 1000);
                }
            } else {
                this.showAlert(result.message || 'حدث خطأ أثناء الحفظ', 'danger');
            }
        } catch (error) {
            this.showAlert('حدث خطأ في الاتصال', 'danger');
        } finally {
            // إعادة تعيين زر الإرسال
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.innerHTML = 'حفظ';
            }
        }
    }

    setupValidation() {
        // التحقق من صحة البيانات
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                }
            });
        });
    }

    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'هذا الحقل مطلوب');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });

        return isValid;
    }

    showFieldError(field, message) {
        this.clearFieldError(field);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error text-danger small mt-1';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
        field.classList.add('is-invalid');
    }

    clearFieldError(field) {
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        field.classList.remove('is-invalid');
    }

    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.main-content') || document.body;
        container.insertBefore(alertDiv, container.firstChild);

        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
}

// إدارة الجداول
class TableManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupSearch();
        this.setupSorting();
        this.setupPagination();
    }

    setupSearch() {
        const searchInputs = document.querySelectorAll('.table-search');
        searchInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                this.filterTable(e.target);
            });
        });
    }

    filterTable(searchInput) {
        const table = searchInput.closest('.table-container').querySelector('table');
        const rows = table.querySelectorAll('tbody tr');
        const searchTerm = searchInput.value.toLowerCase();

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    }

    setupSorting() {
        const sortableHeaders = document.querySelectorAll('.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', () => {
                this.sortTable(header);
            });
        });
    }

    sortTable(header) {
        const table = header.closest('table');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = !header.classList.contains('sort-asc');

        rows.sort((a, b) => {
            const aText = a.children[columnIndex].textContent.trim();
            const bText = b.children[columnIndex].textContent.trim();
            
            if (isAscending) {
                return aText.localeCompare(bText, 'ar');
            } else {
                return bText.localeCompare(aText, 'ar');
            }
        });

        // إعادة ترتيب الصفوف
        rows.forEach(row => tbody.appendChild(row));

        // تحديث أيقونة الترتيب
        document.querySelectorAll('.sortable').forEach(h => {
            h.classList.remove('sort-asc', 'sort-desc');
        });
        
        header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    }

    setupPagination() {
        // سيتم تطوير هذه الوظيفة لاحقاً
    }
}

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', () => {
    new ThemeManager();
    new SidebarManager();
    new FormManager();
    new TableManager();

    // إضافة تأثيرات الحركة
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
});

// وظائف مساعدة
function generateBarcode() {
    const code = Math.random().toString().substr(2, 12);
    document.getElementById('product_code').value = code;
}

function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('image-preview');
            if (preview) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}
