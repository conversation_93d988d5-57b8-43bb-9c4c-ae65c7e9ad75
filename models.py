
from db import db

# إدارة المستودعات
class Warehouse(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    location = db.Column(db.String(200))
    description = db.Column(db.Text)
    def __repr__(self):
        return f'<Warehouse {self.name}>'



# تصنيفات
class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    def __repr__(self):
        return f'<Category {self.name}>'

# وحدات القياس
class Unit(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    def __repr__(self):
        return f'<Unit {self.name}>'

# حالة المادة
class Status(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    def __repr__(self):
        return f'<Status {self.name}>'

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouse.id'))
    warehouse = db.relationship('Warehouse', backref=db.backref('products', lazy=True))
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'))
    category = db.relationship('Category', backref=db.backref('products', lazy=True))
    unit_id = db.Column(db.Integer, db.ForeignKey('unit.id'))
    unit = db.relationship('Unit', backref=db.backref('products', lazy=True))
    status_id = db.Column(db.Integer, db.ForeignKey('status.id'))
    status = db.relationship('Status', backref=db.backref('products', lazy=True))
    quantity = db.Column(db.Float, default=0)
    notes = db.Column(db.Text)
    image = db.Column(db.String(200))
    def __repr__(self):
        return f'<Product {self.name}>'
