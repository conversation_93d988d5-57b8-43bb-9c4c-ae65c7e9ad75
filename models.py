from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()
from datetime import datetime
import random
import string

class Warehouse(db.Model):
    """نموذج المستودعات"""
    __tablename__ = 'warehouses'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    location = db.Column(db.String(200))
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    products = db.relationship('Product', backref='warehouse_ref', lazy=True)

    def __repr__(self):
        return f'<Warehouse {self.name}>'

class Category(db.Model):
    """نموذج التصنيفات"""
    __tablename__ = 'categories'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    products = db.relationship('Product', backref='category_ref', lazy=True)

    def __repr__(self):
        return f'<Category {self.name}>'

class Unit(db.Model):
    """نموذج وحدات القياس"""
    __tablename__ = 'units'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    symbol = db.Column(db.String(10))
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    products = db.relationship('Product', backref='unit_ref', lazy=True)

    def __repr__(self):
        return f'<Unit {self.name}>'

class Condition(db.Model):
    """نموذج حالة المواد"""
    __tablename__ = 'conditions'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    description = db.Column(db.Text)
    color = db.Column(db.String(7), default='#28a745')  # لون للعرض
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    products = db.relationship('Product', backref='condition_ref', lazy=True)

    def __repr__(self):
        return f'<Condition {self.name}>'

class Customer(db.Model):
    """نموذج العملاء"""
    __tablename__ = 'customers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    exit_invoices = db.relationship('ExitInvoice', backref='customer_ref', lazy=True)

    def __repr__(self):
        return f'<Customer {self.name}>'

class Supplier(db.Model):
    """نموذج الموردين"""
    __tablename__ = 'suppliers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    entry_invoices = db.relationship('EntryInvoice', backref='supplier_ref', lazy=True)
    purchase_orders = db.relationship('PurchaseOrder', backref='supplier_ref', lazy=True)

    def __repr__(self):
        return f'<Supplier {self.name}>'

class Product(db.Model):
    """نموذج المنتجات"""
    __tablename__ = 'products'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), nullable=False, unique=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    initial_quantity = db.Column(db.Float, default=0)
    current_quantity = db.Column(db.Float, default=0)
    image_path = db.Column(db.String(200))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # المفاتيح الخارجية
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'), nullable=False)
    unit_id = db.Column(db.Integer, db.ForeignKey('units.id'), nullable=False)
    condition_id = db.Column(db.Integer, db.ForeignKey('conditions.id'), nullable=False)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)

    def generate_barcode(self):
        """توليد باركود عشوائي"""
        if not self.code:
            self.code = ''.join(random.choices(string.digits, k=12))

    def __repr__(self):
        return f'<Product {self.name}>'

class PurchaseOrder(db.Model):
    """نموذج طلبات الشراء"""
    __tablename__ = 'purchase_orders'

    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(50), nullable=False, unique=True)
    order_date = db.Column(db.Date, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    notes = db.Column(db.Text)
    warehouse_manager = db.Column(db.String(100))
    company_manager = db.Column(db.String(100))
    status = db.Column(db.String(20), default='pending')  # pending, completed, cancelled
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    items = db.relationship('PurchaseOrderItem', backref='order_ref', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<PurchaseOrder {self.order_number}>'

class PurchaseOrderItem(db.Model):
    """نموذج بنود طلبات الشراء"""
    __tablename__ = 'purchase_order_items'

    id = db.Column(db.Integer, primary_key=True)
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=False)
    product_name = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Float, nullable=False)
    received_quantity = db.Column(db.Float, default=0)
    unit = db.Column(db.String(50))
    notes = db.Column(db.Text)
    is_received = db.Column(db.Boolean, default=False)

    def __repr__(self):
        return f'<PurchaseOrderItem {self.product_name}>'

class EntryInvoice(db.Model):
    """نموذج فواتير الإدخال"""
    __tablename__ = 'entry_invoices'

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), nullable=False, unique=True)
    invoice_date = db.Column(db.Date, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    warehouse_manager = db.Column(db.String(100))
    supplier_signature = db.Column(db.String(100))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    items = db.relationship('EntryInvoiceItem', backref='invoice_ref', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<EntryInvoice {self.invoice_number}>'

class EntryInvoiceItem(db.Model):
    """نموذج بنود فواتير الإدخال"""
    __tablename__ = 'entry_invoice_items'

    id = db.Column(db.Integer, primary_key=True)
    entry_invoice_id = db.Column(db.Integer, db.ForeignKey('entry_invoices.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    quantity = db.Column(db.Float, nullable=False)
    notes = db.Column(db.Text)

    # العلاقات
    product = db.relationship('Product', backref='entry_items')

    def __repr__(self):
        return f'<EntryInvoiceItem {self.product.name if self.product else "Unknown"}>'

class ExitInvoice(db.Model):
    """نموذج فواتير الإخراج"""
    __tablename__ = 'exit_invoices'

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), nullable=False, unique=True)
    invoice_date = db.Column(db.Date, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    warehouse_manager = db.Column(db.String(100))
    customer_signature = db.Column(db.String(100))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    items = db.relationship('ExitInvoiceItem', backref='invoice_ref', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<ExitInvoice {self.invoice_number}>'

class ExitInvoiceItem(db.Model):
    """نموذج بنود فواتير الإخراج"""
    __tablename__ = 'exit_invoice_items'

    id = db.Column(db.Integer, primary_key=True)
    exit_invoice_id = db.Column(db.Integer, db.ForeignKey('exit_invoices.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    quantity = db.Column(db.Float, nullable=False)
    notes = db.Column(db.Text)

    # العلاقات
    product = db.relationship('Product', backref='exit_items')

    def __repr__(self):
        return f'<ExitInvoiceItem {self.product.name if self.product else "Unknown"}>'