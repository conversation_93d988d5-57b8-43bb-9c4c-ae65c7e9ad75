<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة إدخال - {{ invoice.invoice_number }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12px; }
            .container { max-width: 100% !important; }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .invoice-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 800px;
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            text-align: center;
        }
        
        .invoice-body {
            padding: 30px;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .invoice-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .table th {
            background-color: #e9ecef;
            font-weight: bold;
            text-align: center;
        }
        
        .table td {
            text-align: center;
            vertical-align: middle;
        }
        
        .signature-section {
            margin-top: 50px;
            border-top: 2px solid #dee2e6;
            padding-top: 30px;
        }
        
        .signature-box {
            border: 2px dashed #dee2e6;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .print-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- زر الطباعة -->
    <button class="btn btn-primary print-btn no-print" onclick="window.print()">
        <i class="fas fa-print me-2"></i>طباعة
    </button>

    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <h1 class="mb-0">
                <i class="fas fa-file-import me-3"></i>
                فاتورة إدخال
            </h1>
            <p class="mb-0 mt-2">نظام إدارة المستودعات</p>
        </div>

        <div class="invoice-body">
            <!-- معلومات الشركة -->
            <div class="company-info">
                <h3 class="text-primary">شركة إدارة المستودعات</h3>
                <p class="text-muted mb-0">العنوان: المملكة العربية السعودية</p>
                <p class="text-muted mb-0">الهاتف: +966 XX XXX XXXX | البريد الإلكتروني: <EMAIL></p>
            </div>

            <!-- تفاصيل الفاتورة -->
            <div class="invoice-details">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الفاتورة
                        </h6>
                        <p><strong>رقم الفاتورة:</strong> {{ invoice.invoice_number }}</p>
                        <p><strong>تاريخ الفاتورة:</strong> {{ invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else '-' }}</p>
                        <p><strong>تاريخ الإنشاء:</strong> {{ invoice.created_at.strftime('%Y-%m-%d %H:%M') if invoice.created_at else '-' }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-truck me-2"></i>
                            معلومات المورد
                        </h6>
                        <p><strong>اسم المورد:</strong> {{ invoice.supplier_ref.name if invoice.supplier_ref else '-' }}</p>
                        <p><strong>الهاتف:</strong> {{ invoice.supplier_ref.phone if invoice.supplier_ref else '-' }}</p>
                        <p><strong>العنوان:</strong> {{ invoice.supplier_ref.address if invoice.supplier_ref else '-' }}</p>
                    </div>
                </div>
                
                {% if invoice.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-primary mb-2">
                            <i class="fas fa-sticky-note me-2"></i>
                            ملاحظات
                        </h6>
                        <p class="mb-0">{{ invoice.notes }}</p>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- جدول البنود -->
            <div class="mb-4">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-list me-2"></i>
                    بنود الفاتورة
                </h6>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th style="width: 5%">#</th>
                                <th style="width: 25%">كود المنتج</th>
                                <th style="width: 35%">اسم المنتج</th>
                                <th style="width: 15%">الكمية</th>
                                <th style="width: 10%">الوحدة</th>
                                <th style="width: 10%">ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in invoice.items %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ item.product.code if item.product else '-' }}</td>
                                <td>{{ item.product.name if item.product else 'منتج محذوف' }}</td>
                                <td><strong>{{ item.quantity }}</strong></td>
                                <td>{{ item.product.unit_ref.name if item.product and item.product.unit_ref else '-' }}</td>
                                <td>{{ item.notes or '-' }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center text-muted">لا توجد بنود</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- إجمالي البنود -->
            <div class="row mb-4">
                <div class="col-md-6 offset-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title text-primary">إجمالي الفاتورة</h6>
                            <div class="d-flex justify-content-between">
                                <span>إجمالي البنود:</span>
                                <strong>{{ invoice.items|length }} بند</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>إجمالي الكمية:</span>
                                <strong>{{ invoice.items|sum(attribute='quantity') }} وحدة</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم التوقيعات -->
            <div class="signature-section">
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-center mb-3">مدير المستودع</h6>
                        <div class="signature-box">
                            {% if invoice.warehouse_manager %}
                                <strong>{{ invoice.warehouse_manager }}</strong>
                            {% else %}
                                <span class="text-muted">التوقيع</span>
                            {% endif %}
                        </div>
                        <p class="text-center mt-2 small text-muted">الاسم والتوقيع</p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-center mb-3">ممثل المورد</h6>
                        <div class="signature-box">
                            {% if invoice.supplier_signature %}
                                <strong>{{ invoice.supplier_signature }}</strong>
                            {% else %}
                                <span class="text-muted">التوقيع</span>
                            {% endif %}
                        </div>
                        <p class="text-center mt-2 small text-muted">الاسم والتوقيع</p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-center mb-3">التاريخ والوقت</h6>
                        <div class="signature-box">
                            <strong id="currentDateTime"></strong>
                        </div>
                        <p class="text-center mt-2 small text-muted">تاريخ ووقت الطباعة</p>
                    </div>
                </div>
            </div>

            <!-- تذييل الفاتورة -->
            <div class="text-center mt-4 pt-3 border-top">
                <p class="text-muted small mb-0">
                    تم إنشاء هذه الفاتورة بواسطة نظام إدارة المستودعات
                </p>
                <p class="text-muted small mb-0">
                    للاستفسارات: <EMAIL> | +966 XX XXX XXXX
                </p>
            </div>
        </div>
    </div>

    <script>
        // تحديث التاريخ والوقت الحالي
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const dateTimeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('currentDateTime').textContent = dateTimeString;
        });

        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
