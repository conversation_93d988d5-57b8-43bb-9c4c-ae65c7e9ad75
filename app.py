from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from datetime import datetime, date
import os
import sqlite3
import barcode
from barcode.writer import ImageWriter
import qrcode
from PIL import Image
import io
import base64
from werkzeug.utils import secure_filename
import uuid

app = Flask(__name__)
app.config['SECRET_KEY'] = 'warehouse_management_system_2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///warehouse.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# إنشاء مجلد الرفع إذا لم يكن موجوداً
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# استيراد النماذج
from models import db, Warehouse, Category, Unit, Condition, Product, Customer, Supplier, PurchaseOrder, PurchaseOrderItem, EntryInvoice, EntryInvoiceItem, ExitInvoice, ExitInvoiceItem

# تهيئة قاعدة البيانات مع التطبيق
db.init_app(app)

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    # جمع الإحصائيات
    total_products = Product.query.count()
    total_warehouses = Warehouse.query.count()
    total_customers = Customer.query.count()
    total_suppliers = Supplier.query.count()

    return render_template('index.html',
                         total_products=total_products,
                         total_warehouses=total_warehouses,
                         total_customers=total_customers,
                         total_suppliers=total_suppliers)

@app.route('/warehouses', methods=['GET', 'POST'])
def warehouses():
    """إدارة المستودعات"""
    if request.method == 'POST':
        warehouse_id = request.form.get('warehouse_id')
        name = request.form.get('name')
        location = request.form.get('location')
        description = request.form.get('description')

        if warehouse_id:
            # تعديل مستودع موجود
            warehouse = Warehouse.query.get_or_404(warehouse_id)
            warehouse.name = name
            warehouse.location = location
            warehouse.description = description
        else:
            # إضافة مستودع جديد
            warehouse = Warehouse(
                name=name,
                location=location,
                description=description
            )
            db.session.add(warehouse)

        try:
            db.session.commit()
            flash('تم حفظ المستودع بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ المستودع', 'error')

        return redirect(url_for('warehouses'))

    warehouses_list = Warehouse.query.order_by(Warehouse.created_at.desc()).all()
    return render_template('warehouses.html', warehouses=warehouses_list)

@app.route('/categories', methods=['GET', 'POST'])
def categories():
    """إدارة التصنيفات"""
    if request.method == 'POST':
        category_id = request.form.get('category_id')
        name = request.form.get('name')
        description = request.form.get('description')

        if category_id:
            # تعديل تصنيف موجود
            category = Category.query.get_or_404(category_id)
            category.name = name
            category.description = description
        else:
            # إضافة تصنيف جديد
            category = Category(
                name=name,
                description=description
            )
            db.session.add(category)

        try:
            db.session.commit()
            flash('تم حفظ التصنيف بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ التصنيف', 'error')

        return redirect(url_for('categories'))

    categories_list = Category.query.order_by(Category.created_at.desc()).all()
    return render_template('categories.html', categories=categories_list)

@app.route('/units', methods=['GET', 'POST'])
def units():
    """إدارة وحدات القياس"""
    if request.method == 'POST':
        unit_id = request.form.get('unit_id')
        name = request.form.get('name')
        symbol = request.form.get('symbol')
        description = request.form.get('description')

        if unit_id:
            # تعديل وحدة موجودة
            unit = Unit.query.get_or_404(unit_id)
            unit.name = name
            unit.symbol = symbol
            unit.description = description
        else:
            # إضافة وحدة جديدة
            unit = Unit(
                name=name,
                symbol=symbol,
                description=description
            )
            db.session.add(unit)

        try:
            db.session.commit()
            flash('تم حفظ وحدة القياس بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ وحدة القياس', 'error')

        return redirect(url_for('units'))

    units_list = Unit.query.order_by(Unit.created_at.desc()).all()
    return render_template('units.html', units=units_list)

@app.route('/conditions', methods=['GET', 'POST'])
def conditions():
    """إدارة حالة المواد"""
    if request.method == 'POST':
        condition_id = request.form.get('condition_id')
        name = request.form.get('name')
        description = request.form.get('description')

        if condition_id:
            # تعديل حالة موجودة
            condition = Condition.query.get_or_404(condition_id)
            condition.name = name
            condition.description = description
        else:
            # إضافة حالة جديدة
            condition = Condition(
                name=name,
                description=description
            )
            db.session.add(condition)

        try:
            db.session.commit()
            flash('تم حفظ حالة المواد بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ حالة المواد', 'error')

        return redirect(url_for('conditions'))

    conditions_list = Condition.query.order_by(Condition.created_at.desc()).all()
    return render_template('conditions.html', conditions=conditions_list)

@app.route('/products', methods=['GET', 'POST'])
def products():
    """إدارة المنتجات"""
    if request.method == 'POST':
        product_id = request.form.get('product_id')
        name = request.form.get('name')
        code = request.form.get('barcode')  # استخدام code بدلاً من barcode
        description = request.form.get('description')
        category_id = request.form.get('category_id')
        unit_id = request.form.get('unit_id')
        condition_id = request.form.get('condition_id')
        current_quantity = float(request.form.get('quantity', 0))
        warehouse_id = request.form.get('warehouse_id')

        # معالجة رفع الصورة
        image_path = None
        if 'image' in request.files:
            file = request.files['image']
            if file and file.filename != '' and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                # إضافة UUID لتجنب تضارب الأسماء
                unique_filename = f"{uuid.uuid4()}_{filename}"
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
                file.save(file_path)
                image_path = f"uploads/{unique_filename}"

        if product_id:
            # تعديل منتج موجود
            product = Product.query.get_or_404(product_id)
            product.name = name
            product.code = code
            product.description = description
            product.category_id = category_id if category_id else None
            product.unit_id = unit_id if unit_id else None
            product.condition_id = condition_id if condition_id else None
            product.current_quantity = current_quantity
            product.warehouse_id = warehouse_id if warehouse_id else None
            if image_path:
                product.image_path = image_path
        else:
            # إضافة منتج جديد
            product = Product(
                name=name,
                code=code,
                description=description,
                category_id=category_id if category_id else None,
                unit_id=unit_id if unit_id else None,
                condition_id=condition_id if condition_id else None,
                current_quantity=current_quantity,
                initial_quantity=current_quantity,
                warehouse_id=warehouse_id if warehouse_id else None,
                image_path=image_path
            )
            db.session.add(product)

        try:
            db.session.commit()
            flash('تم حفظ المنتج بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ المنتج', 'error')

        return redirect(url_for('products'))

    products_list = Product.query.order_by(Product.created_at.desc()).all()
    categories = Category.query.all()
    units = Unit.query.all()
    conditions = Condition.query.all()
    warehouses = Warehouse.query.all()

    return render_template('products.html',
                         products=products_list,
                         categories=categories,
                         units=units,
                         conditions=conditions,
                         warehouses=warehouses)



# ==================== طلبات الشراء ====================
@app.route('/purchase_orders', methods=['GET', 'POST'])
def purchase_orders():
    """صفحة طلبات الشراء"""
    if request.method == 'POST':
        order_id = request.form.get('order_id')
        order_number = request.form.get('order_number')
        order_date_str = request.form.get('order_date')
        supplier_id = request.form.get('supplier_id')
        warehouse_manager = request.form.get('warehouse_manager')
        company_manager = request.form.get('company_manager')
        notes = request.form.get('notes')

        # تحويل التاريخ
        try:
            order_date = datetime.strptime(order_date_str, '%Y-%m-%d').date()
        except:
            flash('تاريخ غير صحيح', 'error')
            return redirect(url_for('purchase_orders'))

        if order_id:
            # تعديل طلب موجود
            order = PurchaseOrder.query.get_or_404(order_id)
            order.order_number = order_number
            order.order_date = order_date
            order.supplier_id = supplier_id
            order.warehouse_manager = warehouse_manager
            order.company_manager = company_manager
            order.notes = notes

            # حذف البنود القديمة
            for item in order.items:
                db.session.delete(item)
        else:
            # إضافة طلب جديد
            order = PurchaseOrder(
                order_number=order_number,
                order_date=order_date,
                supplier_id=supplier_id,
                warehouse_manager=warehouse_manager,
                company_manager=company_manager,
                notes=notes,
                status='pending'
            )
            db.session.add(order)

        # إضافة البنود
        item_counter = 1
        while True:
            item_name = request.form.get(f'item_name_{item_counter}')
            if not item_name:
                break

            item_quantity = request.form.get(f'item_quantity_{item_counter}')
            item_unit = request.form.get(f'item_unit_{item_counter}')
            item_notes = request.form.get(f'item_notes_{item_counter}')

            if item_name and item_quantity:
                item = PurchaseOrderItem(
                    product_name=item_name,
                    quantity=float(item_quantity),
                    unit=item_unit,
                    notes=item_notes
                )
                order.items.append(item)

            item_counter += 1

        try:
            db.session.commit()
            flash('تم حفظ طلب الشراء بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

        return redirect(url_for('purchase_orders'))

    orders = PurchaseOrder.query.order_by(PurchaseOrder.created_at.desc()).all()
    suppliers = Supplier.query.all()
    return render_template('purchase_orders.html', orders=orders, suppliers=suppliers)

@app.route('/entry_invoices', methods=['GET', 'POST'])
def entry_invoices():
    """صفحة فواتير الإدخال"""
    if request.method == 'POST':
        # الحصول على البيانات من النموذج
        invoice_id = request.form.get('invoice_id')
        invoice_number = request.form.get('invoice_number')
        invoice_date_str = request.form.get('invoice_date')
        supplier_id = request.form.get('supplier_id')
        warehouse_manager = request.form.get('warehouse_manager')
        supplier_signature = request.form.get('supplier_signature')
        notes = request.form.get('notes')

        # تحويل التاريخ
        try:
            invoice_date = datetime.strptime(invoice_date_str, '%Y-%m-%d').date()
        except:
            flash('تاريخ غير صحيح', 'error')
            return redirect(url_for('entry_invoices'))

        if invoice_id:
            # تعديل فاتورة موجودة
            invoice = EntryInvoice.query.get_or_404(invoice_id)
            invoice.invoice_number = invoice_number
            invoice.invoice_date = invoice_date
            invoice.supplier_id = supplier_id
            invoice.warehouse_manager = warehouse_manager
            invoice.supplier_signature = supplier_signature
            invoice.notes = notes

            # حذف البنود القديمة
            for item in invoice.items:
                # تقليل كمية المنتج
                if item.product:
                    item.product.current_quantity -= item.quantity
                db.session.delete(item)
        else:
            # إضافة فاتورة جديدة
            invoice = EntryInvoice(
                invoice_number=invoice_number,
                invoice_date=invoice_date,
                supplier_id=supplier_id,
                warehouse_manager=warehouse_manager,
                supplier_signature=supplier_signature,
                notes=notes
            )
            db.session.add(invoice)

        # إضافة البنود
        item_counter = 1
        while True:
            product_id = request.form.get(f'product_id_{item_counter}')
            if not product_id:
                break

            quantity_str = request.form.get(f'quantity_{item_counter}')
            item_notes = request.form.get(f'notes_{item_counter}')

            if product_id and quantity_str:
                try:
                    quantity = float(quantity_str)
                    item = EntryInvoiceItem(
                        product_id=int(product_id),
                        quantity=quantity,
                        notes=item_notes
                    )
                    invoice.items.append(item)

                    # زيادة كمية المنتج في المخزون
                    product = Product.query.get(product_id)
                    if product:
                        product.current_quantity += quantity

                except ValueError:
                    flash(f'كمية غير صحيحة في البند {item_counter}', 'error')

            item_counter += 1

        try:
            db.session.commit()
            flash('تم حفظ فاتورة الإدخال بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

        return redirect(url_for('entry_invoices'))

    invoices = EntryInvoice.query.order_by(EntryInvoice.created_at.desc()).all()
    suppliers = Supplier.query.all()
    categories = Category.query.all()
    units = Unit.query.all()
    conditions = Condition.query.all()
    warehouses = Warehouse.query.all()
    return render_template('entry_invoices.html',
                         invoices=invoices,
                         suppliers=suppliers,
                         categories=categories,
                         units=units,
                         conditions=conditions,
                         warehouses=warehouses)

@app.route('/exit_invoices')
def exit_invoices():
    """صفحة فواتير الإخراج"""
    invoices = ExitInvoice.query.all()
    return render_template('exit_invoices.html', invoices=invoices)

@app.route('/reports')
def reports():
    """صفحة التقارير"""
    return render_template('reports.html')

def generate_barcode(code):
    """توليد باركود"""
    try:
        code128 = barcode.get_barcode_class('code128')
        barcode_instance = code128(code, writer=ImageWriter())
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        return base64.b64encode(buffer.getvalue()).decode()
    except:
        return None

def init_db():
    """تهيئة قاعدة البيانات"""
    with app.app_context():
        db.create_all()
        print("تم إنشاء قاعدة البيانات بنجاح!")

# API endpoints
@app.route('/api/warehouses/<int:warehouse_id>')
def api_get_warehouse(warehouse_id):
    """جلب بيانات مستودع محدد"""
    warehouse = Warehouse.query.get_or_404(warehouse_id)
    return jsonify({
        'id': warehouse.id,
        'name': warehouse.name,
        'location': warehouse.location,
        'description': warehouse.description
    })

@app.route('/api/warehouses/<int:warehouse_id>', methods=['DELETE'])
def api_delete_warehouse(warehouse_id):
    """حذف مستودع"""
    try:
        warehouse = Warehouse.query.get_or_404(warehouse_id)
        db.session.delete(warehouse)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف المستودع بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف المستودع'})

@app.route('/api/categories/<int:category_id>')
def api_get_category(category_id):
    """جلب بيانات تصنيف محدد"""
    category = Category.query.get_or_404(category_id)
    return jsonify({
        'id': category.id,
        'name': category.name,
        'description': category.description
    })

@app.route('/api/categories/<int:category_id>', methods=['DELETE'])
def api_delete_category(category_id):
    """حذف تصنيف"""
    try:
        category = Category.query.get_or_404(category_id)
        db.session.delete(category)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف التصنيف بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف التصنيف'})

@app.route('/api/units/<int:unit_id>')
def api_get_unit(unit_id):
    """جلب بيانات وحدة قياس محددة"""
    unit = Unit.query.get_or_404(unit_id)
    return jsonify({
        'id': unit.id,
        'name': unit.name,
        'symbol': unit.symbol,
        'description': unit.description
    })

@app.route('/api/units/<int:unit_id>', methods=['DELETE'])
def api_delete_unit(unit_id):
    """حذف وحدة قياس"""
    try:
        unit = Unit.query.get_or_404(unit_id)
        db.session.delete(unit)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف وحدة القياس بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف وحدة القياس'})

@app.route('/api/conditions/<int:condition_id>')
def api_get_condition(condition_id):
    """جلب بيانات حالة مواد محددة"""
    condition = Condition.query.get_or_404(condition_id)
    return jsonify({
        'id': condition.id,
        'name': condition.name,
        'description': condition.description
    })

@app.route('/api/conditions/<int:condition_id>', methods=['DELETE'])
def api_delete_condition(condition_id):
    """حذف حالة مواد"""
    try:
        condition = Condition.query.get_or_404(condition_id)
        db.session.delete(condition)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف حالة المواد بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف حالة المواد'})

@app.route('/api/products/<int:product_id>')
def api_get_product(product_id):
    """جلب بيانات منتج محدد"""
    product = Product.query.get_or_404(product_id)
    return jsonify({
        'id': product.id,
        'name': product.name,
        'code': product.code,
        'description': product.description,
        'category_id': product.category_id,
        'unit_id': product.unit_id,
        'condition_id': product.condition_id,
        'current_quantity': product.current_quantity,
        'warehouse_id': product.warehouse_id,
        'image_path': product.image_path
    })

@app.route('/api/products/<int:product_id>', methods=['DELETE'])
def api_delete_product(product_id):
    """حذف منتج"""
    try:
        product = Product.query.get_or_404(product_id)
        db.session.delete(product)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف المنتج بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف المنتج'})

# ==================== العملاء ====================
@app.route('/customers', methods=['GET', 'POST'])
def customers():
    """صفحة إدارة العملاء"""
    if request.method == 'POST':
        customer_id = request.form.get('customer_id')
        name = request.form.get('name')
        phone = request.form.get('phone')
        email = request.form.get('email')
        address = request.form.get('address')

        if customer_id:
            # تعديل عميل موجود
            customer = Customer.query.get_or_404(customer_id)
            customer.name = name
            customer.phone = phone
            customer.email = email
            customer.address = address
        else:
            # إضافة عميل جديد
            customer = Customer(
                name=name,
                phone=phone,
                email=email,
                address=address
            )
            db.session.add(customer)

        try:
            db.session.commit()
            flash('تم حفظ بيانات العميل بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

        return redirect(url_for('customers'))

    customers = Customer.query.order_by(Customer.created_at.desc()).all()
    return render_template('customers.html', customers=customers)

@app.route('/api/customers/<int:customer_id>')
def api_get_customer(customer_id):
    """جلب بيانات عميل محدد"""
    customer = Customer.query.get_or_404(customer_id)
    return jsonify({
        'id': customer.id,
        'name': customer.name,
        'phone': customer.phone,
        'email': customer.email,
        'address': customer.address,
        'created_at': customer.created_at.isoformat() if customer.created_at else None
    })

@app.route('/api/customers/<int:customer_id>', methods=['DELETE'])
def api_delete_customer(customer_id):
    """حذف عميل"""
    try:
        customer = Customer.query.get_or_404(customer_id)
        db.session.delete(customer)
        db.session.commit()
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

# ==================== الموردين ====================
@app.route('/suppliers', methods=['GET', 'POST'])
def suppliers():
    """صفحة إدارة الموردين"""
    if request.method == 'POST':
        supplier_id = request.form.get('supplier_id')
        name = request.form.get('name')
        phone = request.form.get('phone')
        email = request.form.get('email')
        address = request.form.get('address')

        if supplier_id:
            # تعديل مورد موجود
            supplier = Supplier.query.get_or_404(supplier_id)
            supplier.name = name
            supplier.phone = phone
            supplier.email = email
            supplier.address = address
        else:
            # إضافة مورد جديد
            supplier = Supplier(
                name=name,
                phone=phone,
                email=email,
                address=address
            )
            db.session.add(supplier)

        try:
            db.session.commit()
            flash('تم حفظ بيانات المورد بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

        return redirect(url_for('suppliers'))

    suppliers = Supplier.query.order_by(Supplier.created_at.desc()).all()
    return render_template('suppliers.html', suppliers=suppliers)

@app.route('/api/suppliers/<int:supplier_id>')
def api_get_supplier(supplier_id):
    """جلب بيانات مورد محدد"""
    supplier = Supplier.query.get_or_404(supplier_id)
    return jsonify({
        'id': supplier.id,
        'name': supplier.name,
        'phone': supplier.phone,
        'email': supplier.email,
        'address': supplier.address,
        'created_at': supplier.created_at.isoformat() if supplier.created_at else None
    })

@app.route('/api/suppliers/<int:supplier_id>', methods=['DELETE'])
def api_delete_supplier(supplier_id):
    """حذف مورد"""
    try:
        supplier = Supplier.query.get_or_404(supplier_id)
        db.session.delete(supplier)
        db.session.commit()
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

# ==================== API طلبات الشراء ====================
@app.route('/api/purchase_orders/<int:order_id>')
def api_get_purchase_order(order_id):
    """جلب بيانات طلب شراء محدد"""
    order = PurchaseOrder.query.get_or_404(order_id)
    return jsonify({
        'id': order.id,
        'order_number': order.order_number,
        'order_date': order.order_date.isoformat() if order.order_date else None,
        'supplier_id': order.supplier_id,
        'supplier_name': order.supplier_ref.name if order.supplier_ref else None,
        'warehouse_manager': order.warehouse_manager,
        'company_manager': order.company_manager,
        'notes': order.notes,
        'status': order.status,
        'created_at': order.created_at.isoformat() if order.created_at else None,
        'items': [{
            'id': item.id,
            'product_name': item.product_name,
            'quantity': item.quantity,
            'received_quantity': item.received_quantity,
            'unit': item.unit,
            'notes': item.notes,
            'is_received': item.is_received
        } for item in order.items]
    })

@app.route('/api/purchase_orders/<int:order_id>/complete', methods=['POST'])
def api_complete_purchase_order(order_id):
    """إكمال طلب شراء"""
    try:
        order = PurchaseOrder.query.get_or_404(order_id)
        order.status = 'completed'
        db.session.commit()
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/purchase_orders/<int:order_id>/cancel', methods=['POST'])
def api_cancel_purchase_order(order_id):
    """إلغاء طلب شراء"""
    try:
        order = PurchaseOrder.query.get_or_404(order_id)
        order.status = 'cancelled'
        db.session.commit()
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/purchase_orders/<int:order_id>/print')
def print_purchase_order(order_id):
    """طباعة طلب الشراء"""
    order = PurchaseOrder.query.get_or_404(order_id)
    return render_template('print_purchase_order.html', order=order)

# ==================== API فواتير الإدخال ====================
@app.route('/api/entry_invoices')
def api_entry_invoices():
    """API للحصول على قائمة فواتير الإدخال"""
    invoices = EntryInvoice.query.order_by(EntryInvoice.created_at.desc()).all()
    invoices_data = []

    for invoice in invoices:
        invoices_data.append({
            'id': invoice.id,
            'invoice_number': invoice.invoice_number,
            'invoice_date': invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else None,
            'supplier_id': invoice.supplier_id,
            'supplier_name': invoice.supplier_ref.name if invoice.supplier_ref else None,
            'warehouse_manager': invoice.warehouse_manager,
            'supplier_signature': invoice.supplier_signature,
            'notes': invoice.notes,
            'created_at': invoice.created_at.strftime('%Y-%m-%d %H:%M:%S') if invoice.created_at else None,
            'items_count': len(invoice.items)
        })

    return jsonify(invoices_data)

@app.route('/api/entry_invoices/<int:invoice_id>')
def api_entry_invoice_details(invoice_id):
    """API للحصول على تفاصيل فاتورة إدخال محددة"""
    invoice = EntryInvoice.query.get_or_404(invoice_id)

    items_data = []
    for item in invoice.items:
        items_data.append({
            'id': item.id,
            'product_id': item.product_id,
            'product_name': item.product.name if item.product else 'منتج محذوف',
            'product_code': item.product.code if item.product else '',
            'product_unit': item.product.unit_ref.name if item.product and item.product.unit_ref else '',
            'quantity': item.quantity,
            'notes': item.notes
        })

    invoice_data = {
        'id': invoice.id,
        'invoice_number': invoice.invoice_number,
        'invoice_date': invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else None,
        'supplier_id': invoice.supplier_id,
        'supplier_name': invoice.supplier_ref.name if invoice.supplier_ref else None,
        'warehouse_manager': invoice.warehouse_manager,
        'supplier_signature': invoice.supplier_signature,
        'notes': invoice.notes,
        'created_at': invoice.created_at.strftime('%Y-%m-%d %H:%M:%S') if invoice.created_at else None,
        'items': items_data
    }

    return jsonify(invoice_data)

@app.route('/api/entry_invoices/<int:invoice_id>', methods=['DELETE'])
def api_delete_entry_invoice(invoice_id):
    """API لحذف فاتورة إدخال"""
    try:
        invoice = EntryInvoice.query.get_or_404(invoice_id)

        # تقليل كميات المنتجات
        for item in invoice.items:
            if item.product:
                item.product.current_quantity -= item.quantity

        db.session.delete(invoice)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حذف الفاتورة بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/products', methods=['GET', 'POST'])
def api_products():
    """API للحصول على قائمة المنتجات أو إضافة منتج جديد"""
    if request.method == 'GET':
        products = Product.query.all()
        products_data = []

        for product in products:
            products_data.append({
                'id': product.id,
                'code': product.code,
                'name': product.name,
                'current_quantity': product.current_quantity,
                'unit_name': product.unit_ref.name if product.unit_ref else None,
                'category_name': product.category_ref.name if product.category_ref else None,
                'warehouse_name': product.warehouse_ref.name if product.warehouse_ref else None,
                'condition_name': product.condition_ref.name if product.condition_ref else None,
                'category_id': product.category_id,
                'unit_id': product.unit_id,
                'condition_id': product.condition_id,
                'warehouse_id': product.warehouse_id
            })

        return jsonify(products_data)

    elif request.method == 'POST':
        try:
            data = request.get_json()

            # التحقق من البيانات المطلوبة
            required_fields = ['code', 'name', 'category_id', 'unit_id', 'condition_id', 'warehouse_id']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'success': False, 'message': f'حقل {field} مطلوب'}), 400

            # التحقق من عدم تكرار الكود
            existing_product = Product.query.filter_by(code=data['code']).first()
            if existing_product:
                return jsonify({'success': False, 'message': 'كود المنتج موجود مسبقاً'}), 400

            # إنشاء المنتج الجديد
            new_product = Product(
                code=data['code'],
                name=data['name'],
                description=data.get('description', ''),
                category_id=data['category_id'],
                unit_id=data['unit_id'],
                condition_id=data['condition_id'],
                warehouse_id=data['warehouse_id'],
                current_quantity=0,
                notes=data.get('notes', '')
            )

            db.session.add(new_product)
            db.session.commit()

            # إرجاع بيانات المنتج الجديد
            product_data = {
                'id': new_product.id,
                'code': new_product.code,
                'name': new_product.name,
                'current_quantity': new_product.current_quantity,
                'unit_name': new_product.unit_ref.name if new_product.unit_ref else None,
                'category_name': new_product.category_ref.name if new_product.category_ref else None,
                'warehouse_name': new_product.warehouse_ref.name if new_product.warehouse_ref else None,
                'condition_name': new_product.condition_ref.name if new_product.condition_ref else None,
                'category_id': new_product.category_id,
                'unit_id': new_product.unit_id,
                'condition_id': new_product.condition_id,
                'warehouse_id': new_product.warehouse_id
            }

            return jsonify({'success': True, 'product': product_data})

        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/entry_invoices/<int:invoice_id>/print')
def print_entry_invoice(invoice_id):
    """طباعة فاتورة الإدخال"""
    invoice = EntryInvoice.query.get_or_404(invoice_id)
    return render_template('print_entry_invoice.html', invoice=invoice)

if __name__ == '__main__':
    init_db()
    app.run(debug=True, host='0.0.0.0', port=5000)