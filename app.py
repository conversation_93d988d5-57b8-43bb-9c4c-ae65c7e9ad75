from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date
import sqlite3
import os
import uuid
import barcode
from barcode.writer import ImageWriter
from io import BytesIO
import base64
from PIL import Image
import json
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import mm
import shutil

app = Flask(__name__)
app.config['SECRET_KEY'] = 'warehouse_management_system_2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///warehouse.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'

db = SQLAlchemy(app)

# إنشاء مجلد الرفع إذا لم يكن موجوداً
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# نماذج قاعدة البيانات
class Warehouse(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    location = db.Column(db.String(200))
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # علاقة مع المنتجات
    products = db.relationship('Product', backref='warehouse_ref', lazy=True)

class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # علاقة مع المنتجات
    products = db.relationship('Product', backref='category_ref', lazy=True)

class Unit(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    symbol = db.Column(db.String(10))
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # علاقة مع المنتجات
    products = db.relationship('Product', backref='unit_ref', lazy=True)

class Status(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    color = db.Column(db.String(7), default='#28a745')  # لون الحالة
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # علاقة مع المنتجات
    products = db.relationship('Product', backref='status_ref', lazy=True)

class Supplier(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    contact_person = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    contact_person = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    barcode = db.Column(db.String(50), unique=True, nullable=False)
    name = db.Column(db.String(200), nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'), nullable=False)
    unit_id = db.Column(db.Integer, db.ForeignKey('unit.id'), nullable=False)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouse.id'), nullable=False)
    status_id = db.Column(db.Integer, db.ForeignKey('status.id'), nullable=False)
    initial_quantity = db.Column(db.Float, default=0)
    current_quantity = db.Column(db.Float, default=0)
    min_quantity = db.Column(db.Float, default=0)
    notes = db.Column(db.Text)
    image_path = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class PurchaseOrder(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(50), unique=True, nullable=False)
    supplier_name = db.Column(db.String(100), nullable=False)
    order_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, received, partial
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # علاقة مع بنود الطلب
    items = db.relationship('PurchaseOrderItem', backref='order', lazy=True, cascade='all, delete-orphan')

class PurchaseOrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('purchase_order.id'), nullable=False)
    product_name = db.Column(db.String(200), nullable=False)
    quantity_ordered = db.Column(db.Float, nullable=False)
    quantity_received = db.Column(db.Float, default=0)
    unit = db.Column(db.String(50), nullable=False)
    notes = db.Column(db.Text)
    is_received = db.Column(db.Boolean, default=False)

class InvoiceIn(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('supplier.id'), nullable=False)
    invoice_date = db.Column(db.Date, nullable=False)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # علاقة مع المورد
    supplier = db.relationship('Supplier', backref='invoices_in')
    # علاقة مع بنود الفاتورة
    items = db.relationship('InvoiceInItem', backref='invoice', lazy=True, cascade='all, delete-orphan')

class InvoiceInItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice_in.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Float, nullable=False)
    notes = db.Column(db.Text)
    
    # علاقة مع المنتج
    product = db.relationship('Product', backref='invoice_in_items')

class InvoiceOut(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    invoice_date = db.Column(db.Date, nullable=False)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # علاقة مع العميل
    customer = db.relationship('Customer', backref='invoices_out')
    # علاقة مع بنود الفاتورة
    items = db.relationship('InvoiceOutItem', backref='invoice', lazy=True, cascade='all, delete-orphan')

class InvoiceOutItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice_out.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Float, nullable=False)
    notes = db.Column(db.Text)
    
    # علاقة مع المنتج
    product = db.relationship('Product', backref='invoice_out_items')

# الصفحة الرئيسية
@app.route('/')
def index():
    # إحصائيات سريعة
    total_products = Product.query.count()
    total_warehouses = Warehouse.query.count()
    total_categories = Category.query.count()
    low_stock_products = Product.query.filter(Product.current_quantity <= Product.min_quantity).count()
    
    stats = {
        'total_products': total_products,
        'total_warehouses': total_warehouses,
        'total_categories': total_categories,
        'low_stock_products': low_stock_products
    }
    
    return render_template('index.html', stats=stats)

# إدارة المستودعات
@app.route('/warehouses')
def warehouses():
    warehouses = Warehouse.query.all()
    return render_template('warehouses/list.html', warehouses=warehouses)

@app.route('/warehouses/add', methods=['GET', 'POST'])
def add_warehouse():
    if request.method == 'POST':
        warehouse = Warehouse(
            name=request.form['name'],
            location=request.form['location'],
            description=request.form['description']
        )
        db.session.add(warehouse)
        db.session.commit()
        flash('تم إضافة المستودع بنجاح!', 'success')
        return redirect(url_for('warehouses'))
    return render_template('warehouses/add.html')

@app.route('/warehouses/edit/<int:id>', methods=['GET', 'POST'])
def edit_warehouse(id):
    warehouse = Warehouse.query.get_or_404(id)
    if request.method == 'POST':
        warehouse.name = request.form['name']
        warehouse.location = request.form['location']
        warehouse.description = request.form['description']
        db.session.commit()
        flash('تم تحديث المستودع بنجاح!', 'success')
        return redirect(url_for('warehouses'))
    return render_template('warehouses/edit.html', warehouse=warehouse)

@app.route('/warehouses/delete/<int:id>')
def delete_warehouse(id):
    warehouse = Warehouse.query.get_or_404(id)
    db.session.delete(warehouse)
    db.session.commit()
    flash('تم حذف المستودع بنجاح!', 'success')
    return redirect(url_for('warehouses'))

@app.route('/warehouses/view/<int:id>')
def view_warehouse(id):
    warehouse = Warehouse.query.get_or_404(id)
    products = Product.query.filter_by(warehouse_id=id).all()
    return render_template('warehouses/view.html', warehouse=warehouse, products=products)

# إدارة التصنيفات
@app.route('/categories')
def categories():
    categories = Category.query.all()
    return render_template('categories/list.html', categories=categories)

@app.route('/categories/add', methods=['GET', 'POST'])
def add_category():
    if request.method == 'POST':
        category = Category(
            name=request.form['name'],
            description=request.form['description']
        )
        db.session.add(category)
        db.session.commit()
        flash('تم إضافة التصنيف بنجاح!', 'success')
        return redirect(url_for('categories'))
    return render_template('categories/add.html')

@app.route('/categories/edit/<int:id>', methods=['GET', 'POST'])
def edit_category(id):
    category = Category.query.get_or_404(id)
    if request.method == 'POST':
        category.name = request.form['name']
        category.description = request.form['description']
        db.session.commit()
        flash('تم تحديث التصنيف بنجاح!', 'success')
        return redirect(url_for('categories'))
    return render_template('categories/edit.html', category=category)

@app.route('/categories/delete/<int:id>')
def delete_category(id):
    category = Category.query.get_or_404(id)
    db.session.delete(category)
    db.session.commit()
    flash('تم حذف التصنيف بنجاح!', 'success')
    return redirect(url_for('categories'))

@app.route('/categories/view/<int:id>')
def view_category(id):
    category = Category.query.get_or_404(id)
    products = Product.query.filter_by(category_id=id).all()
    return render_template('categories/view.html', category=category, products=products)

# إدارة وحدات القياس
@app.route('/units')
def units():
    units = Unit.query.all()
    return render_template('units/list.html', units=units)

@app.route('/units/add', methods=['GET', 'POST'])
def add_unit():
    if request.method == 'POST':
        unit = Unit(
            name=request.form['name'],
            symbol=request.form['symbol'],
            description=request.form['description']
        )
        db.session.add(unit)
        db.session.commit()
        flash('تم إضافة وحدة القياس بنجاح!', 'success')
        return redirect(url_for('units'))
    return render_template('units/add.html')

@app.route('/units/edit/<int:id>', methods=['GET', 'POST'])
def edit_unit(id):
    unit = Unit.query.get_or_404(id)
    if request.method == 'POST':
        unit.name = request.form['name']
        unit.symbol = request.form['symbol']
        unit.description = request.form['description']
        db.session.commit()
        flash('تم تحديث وحدة القياس بنجاح!', 'success')
        return redirect(url_for('units'))
    return render_template('units/edit.html', unit=unit)

@app.route('/units/delete/<int:id>')
def delete_unit(id):
    unit = Unit.query.get_or_404(id)
    db.session.delete(unit)
    db.session.commit()
    flash('تم حذف وحدة القياس بنجاح!', 'success')
    return redirect(url_for('units'))

@app.route('/units/view/<int:id>')
def view_unit(id):
    unit = Unit.query.get_or_404(id)
    products = Product.query.filter_by(unit_id=id).all()
    return render_template('units/view.html', unit=unit, products=products)

# إدارة حالات المواد
@app.route('/statuses')
def statuses():
    statuses = Status.query.all()
    return render_template('statuses/list.html', statuses=statuses)

@app.route('/statuses/add', methods=['GET', 'POST'])
def add_status():
    if request.method == 'POST':
        status = Status(
            name=request.form['name'],
            color=request.form['color'],
            description=request.form['description']
        )
        db.session.add(status)
        db.session.commit()
        flash('تم إضافة حالة المادة بنجاح!', 'success')
        return redirect(url_for('statuses'))
    return render_template('statuses/add.html')

@app.route('/statuses/edit/<int:id>', methods=['GET', 'POST'])
def edit_status(id):
    status = Status.query.get_or_404(id)
    if request.method == 'POST':
        status.name = request.form['name']
        status.color = request.form['color']
        status.description = request.form['description']
        db.session.commit()
        flash('تم تحديث حالة المادة بنجاح!', 'success')
        return redirect(url_for('statuses'))
    return render_template('statuses/edit.html', status=status)

@app.route('/statuses/delete/<int:id>')
def delete_status(id):
    status = Status.query.get_or_404(id)
    db.session.delete(status)
    db.session.commit()
    flash('تم حذف حالة المادة بنجاح!', 'success')
    return redirect(url_for('statuses'))

@app.route('/statuses/view/<int:id>')
def view_status(id):
    status = Status.query.get_or_404(id)
    products = Product.query.filter_by(status_id=id).all()
    return render_template('statuses/view.html', status=status, products=products)

# دالة مساعدة لتوليد الباركود
def generate_barcode():
    return str(uuid.uuid4().int)[:12]

# دالة مساعدة لإنشاء صورة الباركود
def create_barcode_image(barcode_data):
    try:
        code128 = barcode.get_barcode_class('code128')
        barcode_instance = code128(barcode_data, writer=ImageWriter())
        buffer = BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        return base64.b64encode(buffer.getvalue()).decode()
    except:
        return None

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إضافة بيانات افتراضية
        if not Status.query.first():
            default_statuses = [
                Status(name='جيدة', color='#28a745', description='المادة في حالة جيدة'),
                Status(name='تالفة', color='#dc3545', description='المادة تالفة'),
                Status(name='بحاجة لصيانة', color='#ffc107', description='المادة تحتاج صيانة'),
                Status(name='منتهية الصلاحية', color='#6c757d', description='المادة منتهية الصلاحية')
            ]
            for status in default_statuses:
                db.session.add(status)
        
        if not Unit.query.first():
            default_units = [
                Unit(name='كيلوجرام', symbol='كجم', description='وحدة قياس الوزن'),
                Unit(name='قطعة', symbol='قطعة', description='وحدة قياس العدد'),
                Unit(name='لتر', symbol='لتر', description='وحدة قياس السوائل'),
                Unit(name='متر', symbol='م', description='وحدة قياس الطول'),
                Unit(name='صندوق', symbol='صندوق', description='وحدة قياس التعبئة')
            ]
            for unit in default_units:
                db.session.add(unit)
        
        db.session.commit()
    
    app.run(debug=True, host='0.0.0.0', port=5000)