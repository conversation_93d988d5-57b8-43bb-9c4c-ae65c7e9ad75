from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from datetime import datetime, date
import os
import sqlite3
import barcode
from barcode.writer import ImageWriter
import qrcode
from PIL import Image
import io
import base64
from werkzeug.utils import secure_filename
import uuid

app = Flask(__name__)
app.config['SECRET_KEY'] = 'warehouse_management_system_2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///warehouse.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# إنشاء مجلد الرفع إذا لم يكن موجوداً
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# استيراد النماذج
from models import db, Warehouse, Category, Unit, Condition, Product, Customer, Supplier, PurchaseOrder, PurchaseOrderItem, EntryInvoice, EntryInvoiceItem, ExitInvoice, ExitInvoiceItem

# تهيئة قاعدة البيانات مع التطبيق
db.init_app(app)

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    # جمع الإحصائيات
    total_products = Product.query.count()
    total_warehouses = Warehouse.query.count()
    total_customers = Customer.query.count()
    total_suppliers = Supplier.query.count()

    return render_template('index.html',
                         total_products=total_products,
                         total_warehouses=total_warehouses,
                         total_customers=total_customers,
                         total_suppliers=total_suppliers)

@app.route('/warehouses', methods=['GET', 'POST'])
def warehouses():
    """إدارة المستودعات"""
    if request.method == 'POST':
        warehouse_id = request.form.get('warehouse_id')
        name = request.form.get('name')
        location = request.form.get('location')
        description = request.form.get('description')

        if warehouse_id:
            # تعديل مستودع موجود
            warehouse = Warehouse.query.get_or_404(warehouse_id)
            warehouse.name = name
            warehouse.location = location
            warehouse.description = description
        else:
            # إضافة مستودع جديد
            warehouse = Warehouse(
                name=name,
                location=location,
                description=description
            )
            db.session.add(warehouse)

        try:
            db.session.commit()
            flash('تم حفظ المستودع بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ المستودع', 'error')

        return redirect(url_for('warehouses'))

    warehouses_list = Warehouse.query.order_by(Warehouse.created_at.desc()).all()
    return render_template('warehouses.html', warehouses=warehouses_list)

@app.route('/categories', methods=['GET', 'POST'])
def categories():
    """إدارة التصنيفات"""
    if request.method == 'POST':
        category_id = request.form.get('category_id')
        name = request.form.get('name')
        description = request.form.get('description')

        if category_id:
            # تعديل تصنيف موجود
            category = Category.query.get_or_404(category_id)
            category.name = name
            category.description = description
        else:
            # إضافة تصنيف جديد
            category = Category(
                name=name,
                description=description
            )
            db.session.add(category)

        try:
            db.session.commit()
            flash('تم حفظ التصنيف بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ التصنيف', 'error')

        return redirect(url_for('categories'))

    categories_list = Category.query.order_by(Category.created_at.desc()).all()
    return render_template('categories.html', categories=categories_list)

@app.route('/units', methods=['GET', 'POST'])
def units():
    """إدارة وحدات القياس"""
    if request.method == 'POST':
        unit_id = request.form.get('unit_id')
        name = request.form.get('name')
        symbol = request.form.get('symbol')
        description = request.form.get('description')

        if unit_id:
            # تعديل وحدة موجودة
            unit = Unit.query.get_or_404(unit_id)
            unit.name = name
            unit.symbol = symbol
            unit.description = description
        else:
            # إضافة وحدة جديدة
            unit = Unit(
                name=name,
                symbol=symbol,
                description=description
            )
            db.session.add(unit)

        try:
            db.session.commit()
            flash('تم حفظ وحدة القياس بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ وحدة القياس', 'error')

        return redirect(url_for('units'))

    units_list = Unit.query.order_by(Unit.created_at.desc()).all()
    return render_template('units.html', units=units_list)

@app.route('/conditions', methods=['GET', 'POST'])
def conditions():
    """إدارة حالة المواد"""
    if request.method == 'POST':
        condition_id = request.form.get('condition_id')
        name = request.form.get('name')
        description = request.form.get('description')

        if condition_id:
            # تعديل حالة موجودة
            condition = Condition.query.get_or_404(condition_id)
            condition.name = name
            condition.description = description
        else:
            # إضافة حالة جديدة
            condition = Condition(
                name=name,
                description=description
            )
            db.session.add(condition)

        try:
            db.session.commit()
            flash('تم حفظ حالة المواد بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ حالة المواد', 'error')

        return redirect(url_for('conditions'))

    conditions_list = Condition.query.order_by(Condition.created_at.desc()).all()
    return render_template('conditions.html', conditions=conditions_list)

@app.route('/products', methods=['GET', 'POST'])
def products():
    """إدارة المنتجات"""
    if request.method == 'POST':
        product_id = request.form.get('product_id')
        name = request.form.get('name')
        code = request.form.get('barcode')  # استخدام code بدلاً من barcode
        description = request.form.get('description')
        category_id = request.form.get('category_id')
        unit_id = request.form.get('unit_id')
        condition_id = request.form.get('condition_id')
        current_quantity = float(request.form.get('quantity', 0))
        warehouse_id = request.form.get('warehouse_id')

        # معالجة رفع الصورة
        image_path = None
        if 'image' in request.files:
            file = request.files['image']
            if file and file.filename != '' and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                # إضافة UUID لتجنب تضارب الأسماء
                unique_filename = f"{uuid.uuid4()}_{filename}"
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
                file.save(file_path)
                image_path = f"uploads/{unique_filename}"

        if product_id:
            # تعديل منتج موجود
            product = Product.query.get_or_404(product_id)
            product.name = name
            product.code = code
            product.description = description
            product.category_id = category_id if category_id else None
            product.unit_id = unit_id if unit_id else None
            product.condition_id = condition_id if condition_id else None
            product.current_quantity = current_quantity
            product.warehouse_id = warehouse_id if warehouse_id else None
            if image_path:
                product.image_path = image_path
        else:
            # إضافة منتج جديد
            product = Product(
                name=name,
                code=code,
                description=description,
                category_id=category_id if category_id else None,
                unit_id=unit_id if unit_id else None,
                condition_id=condition_id if condition_id else None,
                current_quantity=current_quantity,
                initial_quantity=current_quantity,
                warehouse_id=warehouse_id if warehouse_id else None,
                image_path=image_path
            )
            db.session.add(product)

        try:
            db.session.commit()
            flash('تم حفظ المنتج بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ المنتج', 'error')

        return redirect(url_for('products'))

    products_list = Product.query.order_by(Product.created_at.desc()).all()
    categories = Category.query.all()
    units = Unit.query.all()
    conditions = Condition.query.all()
    warehouses = Warehouse.query.all()

    return render_template('products.html',
                         products=products_list,
                         categories=categories,
                         units=units,
                         conditions=conditions,
                         warehouses=warehouses)

@app.route('/customers')
def customers():
    """صفحة إدارة العملاء"""
    customers = Customer.query.all()
    return render_template('customers.html', customers=customers)

@app.route('/suppliers')
def suppliers():
    """صفحة إدارة الموردين"""
    suppliers = Supplier.query.all()
    return render_template('suppliers.html', suppliers=suppliers)

@app.route('/purchase_orders')
def purchase_orders():
    """صفحة طلبات الشراء"""
    orders = PurchaseOrder.query.all()
    return render_template('purchase_orders.html', orders=orders)

@app.route('/entry_invoices')
def entry_invoices():
    """صفحة فواتير الإدخال"""
    invoices = EntryInvoice.query.all()
    return render_template('entry_invoices.html', invoices=invoices)

@app.route('/exit_invoices')
def exit_invoices():
    """صفحة فواتير الإخراج"""
    invoices = ExitInvoice.query.all()
    return render_template('exit_invoices.html', invoices=invoices)

@app.route('/reports')
def reports():
    """صفحة التقارير"""
    return render_template('reports.html')

def generate_barcode(code):
    """توليد باركود"""
    try:
        code128 = barcode.get_barcode_class('code128')
        barcode_instance = code128(code, writer=ImageWriter())
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        return base64.b64encode(buffer.getvalue()).decode()
    except:
        return None

def init_db():
    """تهيئة قاعدة البيانات"""
    with app.app_context():
        db.create_all()
        print("تم إنشاء قاعدة البيانات بنجاح!")

# API endpoints
@app.route('/api/warehouses/<int:warehouse_id>')
def api_get_warehouse(warehouse_id):
    """جلب بيانات مستودع محدد"""
    warehouse = Warehouse.query.get_or_404(warehouse_id)
    return jsonify({
        'id': warehouse.id,
        'name': warehouse.name,
        'location': warehouse.location,
        'description': warehouse.description
    })

@app.route('/api/warehouses/<int:warehouse_id>', methods=['DELETE'])
def api_delete_warehouse(warehouse_id):
    """حذف مستودع"""
    try:
        warehouse = Warehouse.query.get_or_404(warehouse_id)
        db.session.delete(warehouse)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف المستودع بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف المستودع'})

@app.route('/api/categories/<int:category_id>')
def api_get_category(category_id):
    """جلب بيانات تصنيف محدد"""
    category = Category.query.get_or_404(category_id)
    return jsonify({
        'id': category.id,
        'name': category.name,
        'description': category.description
    })

@app.route('/api/categories/<int:category_id>', methods=['DELETE'])
def api_delete_category(category_id):
    """حذف تصنيف"""
    try:
        category = Category.query.get_or_404(category_id)
        db.session.delete(category)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف التصنيف بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف التصنيف'})

@app.route('/api/units/<int:unit_id>')
def api_get_unit(unit_id):
    """جلب بيانات وحدة قياس محددة"""
    unit = Unit.query.get_or_404(unit_id)
    return jsonify({
        'id': unit.id,
        'name': unit.name,
        'symbol': unit.symbol,
        'description': unit.description
    })

@app.route('/api/units/<int:unit_id>', methods=['DELETE'])
def api_delete_unit(unit_id):
    """حذف وحدة قياس"""
    try:
        unit = Unit.query.get_or_404(unit_id)
        db.session.delete(unit)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف وحدة القياس بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف وحدة القياس'})

@app.route('/api/conditions/<int:condition_id>')
def api_get_condition(condition_id):
    """جلب بيانات حالة مواد محددة"""
    condition = Condition.query.get_or_404(condition_id)
    return jsonify({
        'id': condition.id,
        'name': condition.name,
        'description': condition.description
    })

@app.route('/api/conditions/<int:condition_id>', methods=['DELETE'])
def api_delete_condition(condition_id):
    """حذف حالة مواد"""
    try:
        condition = Condition.query.get_or_404(condition_id)
        db.session.delete(condition)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف حالة المواد بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف حالة المواد'})

@app.route('/api/products/<int:product_id>')
def api_get_product(product_id):
    """جلب بيانات منتج محدد"""
    product = Product.query.get_or_404(product_id)
    return jsonify({
        'id': product.id,
        'name': product.name,
        'code': product.code,
        'description': product.description,
        'category_id': product.category_id,
        'unit_id': product.unit_id,
        'condition_id': product.condition_id,
        'current_quantity': product.current_quantity,
        'warehouse_id': product.warehouse_id,
        'image_path': product.image_path
    })

@app.route('/api/products/<int:product_id>', methods=['DELETE'])
def api_delete_product(product_id):
    """حذف منتج"""
    try:
        product = Product.query.get_or_404(product_id)
        db.session.delete(product)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف المنتج بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف المنتج'})

if __name__ == '__main__':
    init_db()
    app.run(debug=True, host='0.0.0.0', port=5000)