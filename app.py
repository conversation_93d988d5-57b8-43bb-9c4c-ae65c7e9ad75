from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from datetime import datetime, date
import os
import sqlite3
import barcode
from barcode.writer import ImageWriter
import qrcode
from PIL import Image
import io
import base64

app = Flask(__name__)
app.config['SECRET_KEY'] = 'warehouse_management_system_2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///warehouse.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'

# إنشاء مجلد الرفع إذا لم يكن موجوداً
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# استيراد النماذج
from models import db, Warehouse, Category, Unit, Condition, Product, Customer, Supplier, PurchaseOrder, PurchaseOrderItem, EntryInvoice, EntryInvoiceItem, ExitInvoice, ExitInvoiceItem

# تهيئة قاعدة البيانات مع التطبيق
db.init_app(app)

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    # جمع الإحصائيات
    total_products = Product.query.count()
    total_warehouses = Warehouse.query.count()
    total_customers = Customer.query.count()
    total_suppliers = Supplier.query.count()

    return render_template('index.html',
                         total_products=total_products,
                         total_warehouses=total_warehouses,
                         total_customers=total_customers,
                         total_suppliers=total_suppliers)

@app.route('/warehouses', methods=['GET', 'POST'])
def warehouses():
    """إدارة المستودعات"""
    if request.method == 'POST':
        warehouse_id = request.form.get('warehouse_id')
        name = request.form.get('name')
        location = request.form.get('location')
        description = request.form.get('description')

        if warehouse_id:
            # تعديل مستودع موجود
            warehouse = Warehouse.query.get_or_404(warehouse_id)
            warehouse.name = name
            warehouse.location = location
            warehouse.description = description
        else:
            # إضافة مستودع جديد
            warehouse = Warehouse(
                name=name,
                location=location,
                description=description
            )
            db.session.add(warehouse)

        try:
            db.session.commit()
            flash('تم حفظ المستودع بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ المستودع', 'error')

        return redirect(url_for('warehouses'))

    warehouses_list = Warehouse.query.order_by(Warehouse.created_at.desc()).all()
    return render_template('warehouses.html', warehouses=warehouses_list)

@app.route('/categories')
def categories():
    """صفحة إدارة التصنيفات"""
    categories = Category.query.all()
    return render_template('categories.html', categories=categories)

@app.route('/units')
def units():
    """صفحة إدارة وحدات القياس"""
    units = Unit.query.all()
    return render_template('units.html', units=units)

@app.route('/conditions')
def conditions():
    """صفحة إدارة حالة المواد"""
    conditions = Condition.query.all()
    return render_template('conditions.html', conditions=conditions)

@app.route('/products')
def products():
    """صفحة إدارة المنتجات"""
    products = Product.query.all()
    return render_template('products.html', products=products)

@app.route('/customers')
def customers():
    """صفحة إدارة العملاء"""
    customers = Customer.query.all()
    return render_template('customers.html', customers=customers)

@app.route('/suppliers')
def suppliers():
    """صفحة إدارة الموردين"""
    suppliers = Supplier.query.all()
    return render_template('suppliers.html', suppliers=suppliers)

@app.route('/purchase_orders')
def purchase_orders():
    """صفحة طلبات الشراء"""
    orders = PurchaseOrder.query.all()
    return render_template('purchase_orders.html', orders=orders)

@app.route('/entry_invoices')
def entry_invoices():
    """صفحة فواتير الإدخال"""
    invoices = EntryInvoice.query.all()
    return render_template('entry_invoices.html', invoices=invoices)

@app.route('/exit_invoices')
def exit_invoices():
    """صفحة فواتير الإخراج"""
    invoices = ExitInvoice.query.all()
    return render_template('exit_invoices.html', invoices=invoices)

@app.route('/reports')
def reports():
    """صفحة التقارير"""
    return render_template('reports.html')

def generate_barcode(code):
    """توليد باركود"""
    try:
        code128 = barcode.get_barcode_class('code128')
        barcode_instance = code128(code, writer=ImageWriter())
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        return base64.b64encode(buffer.getvalue()).decode()
    except:
        return None

def init_db():
    """تهيئة قاعدة البيانات"""
    with app.app_context():
        db.create_all()
        print("تم إنشاء قاعدة البيانات بنجاح!")

# API endpoints
@app.route('/api/warehouses/<int:warehouse_id>')
def api_get_warehouse(warehouse_id):
    """جلب بيانات مستودع محدد"""
    warehouse = Warehouse.query.get_or_404(warehouse_id)
    return jsonify({
        'id': warehouse.id,
        'name': warehouse.name,
        'location': warehouse.location,
        'description': warehouse.description
    })

@app.route('/api/warehouses/<int:warehouse_id>', methods=['DELETE'])
def api_delete_warehouse(warehouse_id):
    """حذف مستودع"""
    try:
        warehouse = Warehouse.query.get_or_404(warehouse_id)
        db.session.delete(warehouse)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف المستودع بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف المستودع'})

if __name__ == '__main__':
    init_db()
    app.run(debug=True, host='0.0.0.0', port=5000)