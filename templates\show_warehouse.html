{% extends 'index.html' %}
{% block content %}
<div class="container mt-5">
    <h2>تفاصيل المستودع: {{ warehouse.name }}</h2>
    <p><strong>الموقع:</strong> {{ warehouse.location }}</p>
    <p><strong>الوصف:</strong> {{ warehouse.description }}</p>
    <hr>
    <h4>المواد في هذا المستودع</h4>
    {% if products %}
    <table class="table table-bordered table-striped text-center">
        <thead class="table-dark">
            <tr>
                <th>#</th>
                <th>كود المادة</th>
                <th>اسم المادة</th>
                <!-- يمكن إضافة أعمدة أخرى لاحقاً -->
            </tr>
        </thead>
        <tbody>
            {% for product in products %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>{{ product.code }}</td>
                <td>{{ product.name }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="alert alert-info">لا توجد مواد في هذا المستودع حالياً.</div>
    {% endif %}
    <a href="{{ url_for('warehouses') }}" class="btn btn-secondary mt-3">رجوع</a>
</div>
{% endblock %}
