/* متغيرات CSS للألوان */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white: #ffffff;
    --black: #000000;
    
    /* الوضع العادي */
    --bg-color: #ffffff;
    --text-color: #212529;
    --card-bg: #ffffff;
    --border-color: #dee2e6;
    --navbar-bg: #ffffff;
    --sidebar-bg: #f8f9fa;
    --hover-bg: #f8f9fa;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* الوضع الداكن */
[data-theme="dark"] {
    --bg-color: #121212;
    --text-color: #ffffff;
    --card-bg: #1e1e1e;
    --border-color: #333333;
    --navbar-bg: #1e1e1e;
    --sidebar-bg: #1a1a1a;
    --hover-bg: #2a2a2a;
    --shadow: 0 0.125rem 0.25rem rgba(255, 255, 255, 0.1);
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: all 0.3s ease;
    direction: rtl;
    text-align: right;
}

/* شريط التنقل */
.navbar {
    background-color: var(--navbar-bg) !important;
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    color: var(--text-color) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: var(--hover-bg);
    color: var(--primary-color) !important;
}

/* الشريط الجانبي */
.sidebar {
    background-color: var(--sidebar-bg);
    border-left: 1px solid var(--border-color);
    min-height: calc(100vh - 76px);
    padding: 1.5rem 0;
    box-shadow: var(--shadow);
}

.sidebar .nav-link {
    color: var(--text-color);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    border-right: 3px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: var(--hover-bg);
    border-right-color: var(--primary-color);
    color: var(--primary-color);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    padding: 2rem;
    min-height: calc(100vh - 76px);
}

/* البطاقات */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
    font-weight: 600;
}

[data-theme="dark"] .card-header {
    background-color: var(--dark-color);
}

.card-body {
    padding: 1.5rem;
}

/* الأزرار */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--white);
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.btn-info {
    background-color: var(--info-color);
    color: var(--white);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--white);
}

/* النماذج */
.form-control {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    background-color: var(--card-bg);
    color: var(--text-color);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

/* الجداول */
.table {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th {
    background-color: var(--light-color);
    border-color: var(--border-color);
    font-weight: 600;
    padding: 1rem;
}

[data-theme="dark"] .table th {
    background-color: var(--dark-color);
}

.table td {
    border-color: var(--border-color);
    padding: 0.75rem 1rem;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--hover-bg);
}

/* مفتاح الوضع الداكن */
.theme-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* الرسائل */
.alert {
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* الإحصائيات */
.stats-card {
    text-align: center;
    padding: 2rem;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: var(--white);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.2);
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 76px;
        right: -100%;
        width: 280px;
        height: calc(100vh - 76px);
        z-index: 1000;
        transition: right 0.3s ease;
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .theme-toggle {
        top: 10px;
        left: 10px;
        width: 40px;
        height: 40px;
    }
}

/* تحسينات إضافية */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
