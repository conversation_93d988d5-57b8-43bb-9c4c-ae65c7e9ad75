{% extends "base.html" %}

{% block title %}إدارة حالة المواد - نظام إدارة المستودعات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">إدارة حالة المواد</h1>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#conditionModal">
        <i class="fas fa-plus me-2"></i>
        إضافة حالة جديدة
    </button>
</div>

<!-- جدول حالة المواد -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-check me-2"></i>
                    قائمة حالة المواد
                </h5>
            </div>
            <div class="col-auto">
                <div class="input-group">
                    <input type="text" class="form-control table-search" placeholder="البحث في الحالات...">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if conditions %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th class="sortable">اسم الحالة</th>
                        <th class="sortable">الوصف</th>
                        <th class="sortable">عدد المنتجات</th>
                        <th class="sortable">تاريخ الإنشاء</th>
                        <th width="200">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for condition in conditions %}
                    <tr>
                        <td>
                            <strong>{{ condition.name }}</strong>
                        </td>
                        <td>{{ condition.description or '-' }}</td>
                        <td>
                            <span class="badge bg-primary">
                                {{ condition.products|length }}
                            </span>
                        </td>
                        <td>{{ condition.created_at.strftime('%Y/%m/%d') }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-info" 
                                        onclick="viewCondition({{ condition.id }})" 
                                        title="عرض المنتجات">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-warning" 
                                        onclick="editCondition({{ condition.id }})" 
                                        title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger delete-btn" 
                                        onclick="deleteCondition({{ condition.id }})" 
                                        data-item-name="{{ condition.name }}"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد حالات للمواد</h5>
            <p class="text-muted">ابدأ بإضافة حالة جديدة للمواد</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#conditionModal">
                <i class="fas fa-plus me-2"></i>
                إضافة حالة جديدة
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- نافذة إضافة/تعديل حالة المواد -->
<div class="modal fade" id="conditionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="conditionModalTitle">إضافة حالة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="conditionForm" method="POST" action="{{ url_for('conditions') }}">
                <div class="modal-body">
                    <input type="hidden" id="condition_id" name="condition_id">
                    
                    <div class="mb-3">
                        <label for="condition_name" class="form-label">اسم الحالة *</label>
                        <input type="text" class="form-control" id="condition_name" name="name" required>
                        <div class="form-text">مثال: جديد، مستعمل، تالف، منتهي الصلاحية</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="condition_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="condition_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentConditionId = null;

function addCondition() {
    currentConditionId = null;
    document.getElementById('conditionModalTitle').textContent = 'إضافة حالة جديدة';
    document.getElementById('conditionForm').reset();
    document.getElementById('condition_id').value = '';
}

function editCondition(id) {
    currentConditionId = id;
    document.getElementById('conditionModalTitle').textContent = 'تعديل حالة المواد';
    
    fetch(`/api/conditions/${id}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('condition_id').value = data.id;
            document.getElementById('condition_name').value = data.name;
            document.getElementById('condition_description').value = data.description || '';
            
            new bootstrap.Modal(document.getElementById('conditionModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في جلب بيانات حالة المواد');
        });
}

function viewCondition(id) {
    alert('سيتم تطوير هذه الميزة قريباً');
}

function deleteCondition(id) {
    if (confirm('هل أنت متأكد من حذف هذه الحالة؟')) {
        fetch(`/api/conditions/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

document.getElementById('conditionModal').addEventListener('show.bs.modal', function (event) {
    if (!currentConditionId) {
        addCondition();
    }
});
</script>
{% endblock %}
