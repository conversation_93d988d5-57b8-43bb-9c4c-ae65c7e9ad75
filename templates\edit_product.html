{% extends 'index.html' %}
{% block content %}
<div class="container mt-5">
    <h2>تعديل منتج</h2>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">كود المنتج</label>
            <input type="text" name="code" class="form-control" value="{{ product.code }}" required>
        </div>
        <div class="mb-3">
            <label class="form-label">اسم المنتج</label>
            <input type="text" name="name" class="form-control" value="{{ product.name }}" required>
        </div>
        <div class="mb-3">
            <label class="form-label">المستودع</label>
            <select name="warehouse_id" class="form-select" required>
                {% for warehouse in warehouses %}
                <option value="{{ warehouse.id }}" {% if product.warehouse_id == warehouse.id %}selected{% endif %}>{{ warehouse.name }}</option>
                {% endfor %}
            </select>
        </div>
        <button type="submit" class="btn btn-primary">تعديل</button>
        <a href="{{ url_for('products') }}" class="btn btn-secondary">رجوع</a>
    </form>
</div>
{% endblock %}
