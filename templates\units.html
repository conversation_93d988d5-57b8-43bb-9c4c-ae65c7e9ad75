{% extends "base.html" %}

{% block title %}إدارة وحدات القياس - نظام إدارة المستودعات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">إدارة وحدات القياس</h1>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#unitModal">
        <i class="fas fa-plus me-2"></i>
        إضافة وحدة جديدة
    </button>
</div>

<!-- جدول وحدات القياس -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ruler me-2"></i>
                    قائمة وحدات القياس
                </h5>
            </div>
            <div class="col-auto">
                <div class="input-group">
                    <input type="text" class="form-control table-search" placeholder="البحث في الوحدات...">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if units %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th class="sortable">اسم الوحدة</th>
                        <th class="sortable">الرمز</th>
                        <th class="sortable">الوصف</th>
                        <th class="sortable">عدد المنتجات</th>
                        <th class="sortable">تاريخ الإنشاء</th>
                        <th width="200">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for unit in units %}
                    <tr>
                        <td>
                            <strong>{{ unit.name }}</strong>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ unit.symbol }}</span>
                        </td>
                        <td>{{ unit.description or '-' }}</td>
                        <td>
                            <span class="badge bg-primary">
                                {{ unit.products|length }}
                            </span>
                        </td>
                        <td>{{ unit.created_at.strftime('%Y/%m/%d') }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-info"
                                        onclick="viewUnit({{ unit.id }})"
                                        title="عرض المنتجات">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-warning"
                                        onclick="editUnit({{ unit.id }})"
                                        title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger delete-btn"
                                        onclick="deleteUnit({{ unit.id }})"
                                        data-item-name="{{ unit.name }}"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-ruler fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد وحدات قياس</h5>
            <p class="text-muted">ابدأ بإضافة وحدة قياس جديدة</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#unitModal">
                <i class="fas fa-plus me-2"></i>
                إضافة وحدة جديدة
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- نافذة إضافة/تعديل وحدة القياس -->
<div class="modal fade" id="unitModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unitModalTitle">إضافة وحدة قياس جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="unitForm" method="POST" action="{{ url_for('units') }}">
                <div class="modal-body">
                    <input type="hidden" id="unit_id" name="unit_id">

                    <div class="mb-3">
                        <label for="unit_name" class="form-label">اسم الوحدة *</label>
                        <input type="text" class="form-control" id="unit_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="unit_symbol" class="form-label">الرمز *</label>
                        <input type="text" class="form-control" id="unit_symbol" name="symbol" required maxlength="10">
                        <div class="form-text">مثال: كجم، لتر، قطعة، متر</div>
                    </div>

                    <div class="mb-3">
                        <label for="unit_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="unit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentUnitId = null;

function addUnit() {
    currentUnitId = null;
    document.getElementById('unitModalTitle').textContent = 'إضافة وحدة قياس جديدة';
    document.getElementById('unitForm').reset();
    document.getElementById('unit_id').value = '';
}

function editUnit(id) {
    currentUnitId = id;
    document.getElementById('unitModalTitle').textContent = 'تعديل وحدة القياس';

    fetch(`/api/units/${id}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('unit_id').value = data.id;
            document.getElementById('unit_name').value = data.name;
            document.getElementById('unit_symbol').value = data.symbol;
            document.getElementById('unit_description').value = data.description || '';

            new bootstrap.Modal(document.getElementById('unitModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في جلب بيانات وحدة القياس');
        });
}

function viewUnit(id) {
    alert('سيتم تطوير هذه الميزة قريباً');
}

function deleteUnit(id) {
    if (confirm('هل أنت متأكد من حذف هذه الوحدة؟')) {
        fetch(`/api/units/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

document.getElementById('unitModal').addEventListener('show.bs.modal', function (event) {
    if (!currentUnitId) {
        addUnit();
    }
});
</script>
{% endblock %}