{% extends 'index.html' %}
{% block content %}
<div class="container mt-5">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>إدارة وحدات القياس</h2>
        <a href="{{ url_for('add_unit') }}" class="btn btn-success">إضافة وحدة</a>
    </div>
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ category }}">{{ message }}</div>
        {% endfor %}
      {% endif %}
    {% endwith %}
    <table class="table table-bordered table-striped text-center">
        <thead class="table-dark">
            <tr>
                <th>#</th>
                <th>اسم الوحدة</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for unit in units %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>{{ unit.name }}</td>
                <td>
                    <a href="{{ url_for('edit_unit', id=unit.id) }}" class="btn btn-primary btn-sm">تعديل</a>
                    <form action="{{ url_for('delete_unit', id=unit.id) }}" method="post" style="display:inline-block;">
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟')">حذف</button>
                    </form>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
