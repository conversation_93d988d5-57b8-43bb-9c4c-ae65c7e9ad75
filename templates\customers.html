{% extends "base.html" %}

{% block title %}إدارة العملاء{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users me-2"></i>إدارة العملاء</h2>
                <button type="button" class="btn btn-primary" onclick="addCustomer()">
                    <i class="fas fa-plus me-1"></i>إضافة عميل جديد
                </button>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="البحث بالاسم أو الهاتف أو البريد الإلكتروني...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                <i class="fas fa-times me-1"></i>مسح الفلاتر
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-success w-100" onclick="exportCustomers()">
                                <i class="fas fa-file-excel me-1"></i>تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول العملاء -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="50">#</th>
                                    <th class="sortable">اسم العميل</th>
                                    <th class="sortable">رقم الهاتف</th>
                                    <th class="sortable">البريد الإلكتروني</th>
                                    <th>العنوان</th>
                                    <th class="sortable">تاريخ الإضافة</th>
                                    <th width="200">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="customersTableBody">
                                {% for customer in customers %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <strong>{{ customer.name }}</strong>
                                    </td>
                                    <td>
                                        {% if customer.phone %}
                                        <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                            <i class="fas fa-phone me-1"></i>{{ customer.phone }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if customer.email %}
                                        <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                            <i class="fas fa-envelope me-1"></i>{{ customer.email }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;" 
                                              title="{{ customer.address or '-' }}">
                                            {{ customer.address or '-' }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ customer.created_at.strftime('%Y-%m-%d') if customer.created_at else '-' }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                    onclick="viewCustomer({{ customer.id }})" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="editCustomer({{ customer.id }})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteCustomer({{ customer.id }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-users fa-3x mb-3 d-block"></i>
                                        لا توجد عملاء مسجلين حتى الآن
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة/تعديل العميل -->
<div class="modal fade" id="customerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerModalTitle">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="customerForm" method="POST" action="{{ url_for('customers') }}">
                <div class="modal-body">
                    <input type="hidden" id="customer_id" name="customer_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_name" class="form-label">اسم العميل *</label>
                                <input type="text" class="form-control" id="customer_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="customer_phone" name="phone">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="customer_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="customer_email" name="email">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="customer_address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="customer_address" name="address" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة عرض تفاصيل العميل -->
<div class="modal fade" id="viewCustomerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل العميل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="customerDetails">
                <!-- سيتم ملء التفاصيل هنا بواسطة JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let currentCustomers = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadCustomers();
    setupSearch();
});

// تحميل العملاء
function loadCustomers() {
    // سيتم تنفيذ هذا لاحقاً عند إضافة API
}

// إعداد البحث
function setupSearch() {
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('input', function() {
        filterCustomers(this.value);
    });
}

// فلترة العملاء
function filterCustomers(searchTerm) {
    const rows = document.querySelectorAll('#customersTableBody tr');
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const shouldShow = text.includes(searchTerm.toLowerCase());
        row.style.display = shouldShow ? '' : 'none';
    });
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    filterCustomers('');
}

// إضافة عميل جديد
function addCustomer() {
    document.getElementById('customerModalTitle').textContent = 'إضافة عميل جديد';
    document.getElementById('customerForm').reset();
    document.getElementById('customer_id').value = '';
    new bootstrap.Modal(document.getElementById('customerModal')).show();
}

// تعديل عميل
function editCustomer(customerId) {
    fetch(`/api/customers/${customerId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('customerModalTitle').textContent = 'تعديل العميل';
            document.getElementById('customer_id').value = data.id;
            document.getElementById('customer_name').value = data.name;
            document.getElementById('customer_phone').value = data.phone || '';
            document.getElementById('customer_email').value = data.email || '';
            document.getElementById('customer_address').value = data.address || '';
            
            new bootstrap.Modal(document.getElementById('customerModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحميل بيانات العميل');
        });
}

// عرض تفاصيل العميل
function viewCustomer(customerId) {
    fetch(`/api/customers/${customerId}`)
        .then(response => response.json())
        .then(data => {
            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>اسم العميل:</h6>
                        <p>${data.name}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>رقم الهاتف:</h6>
                        <p>${data.phone || '-'}</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <h6>البريد الإلكتروني:</h6>
                        <p>${data.email || '-'}</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <h6>العنوان:</h6>
                        <p>${data.address || '-'}</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <h6>تاريخ الإضافة:</h6>
                        <p>${new Date(data.created_at).toLocaleDateString('ar-SA')}</p>
                    </div>
                </div>
            `;
            document.getElementById('customerDetails').innerHTML = detailsHtml;
            new bootstrap.Modal(document.getElementById('viewCustomerModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحميل بيانات العميل');
        });
}

// حذف عميل
function deleteCustomer(customerId) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
        fetch(`/api/customers/${customerId}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('حدث خطأ في حذف العميل');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في حذف العميل');
        });
    }
}

// تصدير العملاء
function exportCustomers() {
    // سيتم تنفيذ هذا لاحقاً
    alert('ميزة التصدير ستكون متاحة قريباً');
}
</script>
{% endblock %}
