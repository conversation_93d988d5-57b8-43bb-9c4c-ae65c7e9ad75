<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة طلب الشراء - {{ order.order_number }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12px; }
            .container { max-width: 100% !important; }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: white;
        }
        
        .header-section {
            border-bottom: 3px solid #007bff;
            margin-bottom: 30px;
            padding-bottom: 20px;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .order-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .items-table {
            border: 2px solid #007bff;
        }
        
        .items-table th {
            background: #007bff;
            color: white;
            text-align: center;
            padding: 12px;
        }
        
        .items-table td {
            padding: 10px;
            border: 1px solid #dee2e6;
        }
        
        .signature-section {
            margin-top: 50px;
            border-top: 1px solid #dee2e6;
            padding-top: 30px;
        }
        
        .signature-box {
            border: 1px solid #dee2e6;
            height: 80px;
            margin-top: 10px;
        }
        
        .status-badge {
            font-size: 1.2em;
            padding: 8px 16px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- أزرار الطباعة -->
        <div class="no-print mb-3">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print me-2"></i>طباعة
            </button>
            <button onclick="window.close()" class="btn btn-secondary ms-2">
                <i class="fas fa-times me-2"></i>إغلاق
            </button>
        </div>

        <!-- رأس الطلب -->
        <div class="header-section">
            <div class="company-info">
                <h1 class="mb-1">نظام إدارة المستودعات</h1>
                <h2 class="h4 text-primary mb-3">طلب شراء</h2>
                <div class="row">
                    <div class="col-md-6">
                        <strong>رقم الطلب:</strong> {{ order.order_number }}
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الطلب:</strong> {{ order.order_date.strftime('%Y-%m-%d') if order.order_date else '-' }}
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الطلب -->
        <div class="order-info">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-primary mb-3">معلومات المورد</h5>
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>اسم المورد:</strong></td>
                            <td>{{ order.supplier_ref.name if order.supplier_ref else '-' }}</td>
                        </tr>
                        <tr>
                            <td><strong>رقم الهاتف:</strong></td>
                            <td>{{ order.supplier_ref.phone if order.supplier_ref and order.supplier_ref.phone else '-' }}</td>
                        </tr>
                        <tr>
                            <td><strong>البريد الإلكتروني:</strong></td>
                            <td>{{ order.supplier_ref.email if order.supplier_ref and order.supplier_ref.email else '-' }}</td>
                        </tr>
                        <tr>
                            <td><strong>العنوان:</strong></td>
                            <td>{{ order.supplier_ref.address if order.supplier_ref and order.supplier_ref.address else '-' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5 class="text-primary mb-3">معلومات الطلب</h5>
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>حالة الطلب:</strong></td>
                            <td>
                                {% if order.status == 'pending' %}
                                <span class="badge bg-warning status-badge">قيد الانتظار</span>
                                {% elif order.status == 'completed' %}
                                <span class="badge bg-success status-badge">مكتمل</span>
                                {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger status-badge">ملغي</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>مدير المستودع:</strong></td>
                            <td>{{ order.warehouse_manager or '-' }}</td>
                        </tr>
                        <tr>
                            <td><strong>مدير الشركة:</strong></td>
                            <td>{{ order.company_manager or '-' }}</td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الإنشاء:</strong></td>
                            <td>{{ order.created_at.strftime('%Y-%m-%d %H:%M') if order.created_at else '-' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            {% if order.notes %}
            <div class="mt-3">
                <h6 class="text-primary">ملاحظات:</h6>
                <p class="mb-0">{{ order.notes }}</p>
            </div>
            {% endif %}
        </div>

        <!-- جدول البنود -->
        <div class="mb-4">
            <h5 class="text-primary mb-3">بنود الطلب</h5>
            <table class="table items-table">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="40%">اسم المنتج</th>
                        <th width="15%">الكمية المطلوبة</th>
                        <th width="15%">الكمية المستلمة</th>
                        <th width="10%">الوحدة</th>
                        <th width="15%">ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in order.items %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td><strong>{{ item.product_name }}</strong></td>
                        <td class="text-center">{{ item.quantity }}</td>
                        <td class="text-center">
                            {% if item.received_quantity > 0 %}
                            <span class="text-success">{{ item.received_quantity }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">{{ item.unit or '-' }}</td>
                        <td>{{ item.notes or '-' }}</td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center text-muted">لا توجد بنود في هذا الطلب</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>



        <!-- قسم التوقيعات -->
        <div class="signature-section">
            <div class="row">
                <div class="col-md-4">
                    <h6 class="text-center">مدير المستودع</h6>
                    <div class="signature-box"></div>
                    <p class="text-center mt-2 small">{{ order.warehouse_manager or '_______________' }}</p>
                    <p class="text-center small text-muted">التوقيع والتاريخ</p>
                </div>
                <div class="col-md-4">
                    <h6 class="text-center">مدير الشركة</h6>
                    <div class="signature-box"></div>
                    <p class="text-center mt-2 small">{{ order.company_manager or '_______________' }}</p>
                    <p class="text-center small text-muted">التوقيع والتاريخ</p>
                </div>
                <div class="col-md-4">
                    <h6 class="text-center">المورد</h6>
                    <div class="signature-box"></div>
                    <p class="text-center mt-2 small">{{ order.supplier_ref.name if order.supplier_ref else '_______________' }}</p>
                    <p class="text-center small text-muted">التوقيع والتاريخ</p>
                </div>
            </div>
        </div>

        <!-- تذييل الطباعة -->
        <div class="text-center mt-5 small text-muted">
            <p>تم إنشاء هذا الطلب بواسطة نظام إدارة المستودعات</p>
            <p>تاريخ الطباعة: <span id="printDate"></span></p>
        </div>
    </div>

    <script>
        // تعيين تاريخ الطباعة
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const printDate = now.getFullYear() + '-' +
                             String(now.getMonth() + 1).padStart(2, '0') + '-' +
                             String(now.getDate()).padStart(2, '0') + ' ' +
                             String(now.getHours()).padStart(2, '0') + ':' +
                             String(now.getMinutes()).padStart(2, '0');
            document.getElementById('printDate').textContent = printDate;
        });

        // طباعة تلقائية عند التحميل (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
