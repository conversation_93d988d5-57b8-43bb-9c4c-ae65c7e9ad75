{% extends "base.html" %}

{% block title %}إدارة التصنيفات - نظام إدارة المستودعات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">إدارة التصنيفات</h1>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
        <i class="fas fa-plus me-2"></i>
        إضافة تصنيف جديد
    </button>
</div>

<!-- جدول التصنيفات -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tags me-2"></i>
                    قائمة التصنيفات
                </h5>
            </div>
            <div class="col-auto">
                <div class="input-group">
                    <input type="text" class="form-control table-search" placeholder="البحث في التصنيفات...">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if categories %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th class="sortable">اسم التصنيف</th>
                        <th class="sortable">الوصف</th>
                        <th class="sortable">عدد المنتجات</th>
                        <th class="sortable">تاريخ الإنشاء</th>
                        <th width="200">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for category in categories %}
                    <tr>
                        <td>
                            <strong>{{ category.name }}</strong>
                        </td>
                        <td>{{ category.description or '-' }}</td>
                        <td>
                            <span class="badge bg-primary">
                                {{ category.products|length }}
                            </span>
                        </td>
                        <td>{{ category.created_at.strftime('%Y/%m/%d') }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-info"
                                        onclick="viewCategory({{ category.id }})"
                                        title="عرض المنتجات">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-warning"
                                        onclick="editCategory({{ category.id }})"
                                        title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger delete-btn"
                                        onclick="deleteCategory({{ category.id }})"
                                        data-item-name="{{ category.name }}"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد تصنيفات</h5>
            <p class="text-muted">ابدأ بإضافة تصنيف جديد</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
                <i class="fas fa-plus me-2"></i>
                إضافة تصنيف جديد
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- نافذة إضافة/تعديل التصنيف -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalTitle">إضافة تصنيف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="categoryForm" method="POST" action="{{ url_for('categories') }}">
                <div class="modal-body">
                    <input type="hidden" id="category_id" name="category_id">

                    <div class="mb-3">
                        <label for="category_name" class="form-label">اسم التصنيف *</label>
                        <input type="text" class="form-control" id="category_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="category_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="category_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentCategoryId = null;

function addCategory() {
    currentCategoryId = null;
    document.getElementById('categoryModalTitle').textContent = 'إضافة تصنيف جديد';
    document.getElementById('categoryForm').reset();
    document.getElementById('category_id').value = '';
}

function editCategory(id) {
    currentCategoryId = id;
    document.getElementById('categoryModalTitle').textContent = 'تعديل التصنيف';

    fetch(`/api/categories/${id}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('category_id').value = data.id;
            document.getElementById('category_name').value = data.name;
            document.getElementById('category_description').value = data.description || '';

            new bootstrap.Modal(document.getElementById('categoryModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في جلب بيانات التصنيف');
        });
}

function viewCategory(id) {
    alert('سيتم تطوير هذه الميزة قريباً');
}

function deleteCategory(id) {
    if (confirm('هل أنت متأكد من حذف هذا التصنيف؟')) {
        fetch(`/api/categories/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

document.getElementById('categoryModal').addEventListener('show.bs.modal', function (event) {
    if (!currentCategoryId) {
        addCategory();
    }
});
</script>
{% endblock %}