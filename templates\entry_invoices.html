{% extends "base.html" %}

{% block title %}فواتير الإدخال{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-file-import me-2"></i>
                    فواتير الإدخال
                </h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#invoiceModal" onclick="resetInvoiceForm()">
                        <i class="fas fa-plus me-1"></i>
                        إضافة فاتورة إدخال
                    </button>
                    <button type="button" class="btn btn-info" onclick="loadFromPurchaseOrder()">
                        <i class="fas fa-shopping-cart me-1"></i>
                        من طلب شراء
                    </button>
                </div>
            </div>

            <!-- شريط البحث والفلترة -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="البحث برقم الفاتورة أو المورد...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="supplierFilter">
                                <option value="">جميع الموردين</option>
                                {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="dateFilter" placeholder="تاريخ الفاتورة">
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول فواتير الإدخال -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>تاريخ الفاتورة</th>
                                    <th>المورد</th>
                                    <th>عدد البنود</th>
                                    <th>مدير المستودع</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="invoicesTableBody">
                                {% for invoice in invoices %}
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ invoice.invoice_number }}</strong>
                                    </td>
                                    <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else '-' }}</td>
                                    <td>
                                        <i class="fas fa-truck text-info me-1"></i>
                                        {{ invoice.supplier_ref.name if invoice.supplier_ref else '-' }}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ invoice.items|length }} بند</span>
                                    </td>
                                    <td>{{ invoice.warehouse_manager or '-' }}</td>
                                    <td>{{ invoice.created_at.strftime('%Y-%m-%d %H:%M') if invoice.created_at else '-' }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-info" 
                                                    onclick="viewInvoice({{ invoice.id }})" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-warning" 
                                                    onclick="editInvoice({{ invoice.id }})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-success" 
                                                    onclick="printInvoice({{ invoice.id }})" title="طباعة">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteInvoice({{ invoice.id }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>
                                        لا توجد فواتير إدخال حتى الآن
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة/تعديل فاتورة الإدخال -->
<div class="modal fade" id="invoiceModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="invoiceModalTitle">إضافة فاتورة إدخال</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="invoiceForm">
                <div class="modal-body">
                    <input type="hidden" id="invoice_id" name="invoice_id">
                    
                    <!-- معلومات الفاتورة الأساسية -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="invoice_number" class="form-label">رقم الفاتورة *</label>
                                <input type="text" class="form-control" id="invoice_number" name="invoice_number" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="invoice_date" class="form-label">تاريخ الفاتورة *</label>
                                <input type="date" class="form-control" id="invoice_date" name="invoice_date" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="supplier_id" class="form-label">المورد *</label>
                                <select class="form-select" id="supplier_id" name="supplier_id" required>
                                    <option value="">اختر المورد</option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="warehouse_manager" class="form-label">مدير المستودع</label>
                                <input type="text" class="form-control" id="warehouse_manager" name="warehouse_manager" 
                                       placeholder="اسم مدير المستودع">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="supplier_signature" class="form-label">توقيع المورد</label>
                                <input type="text" class="form-control" id="supplier_signature" name="supplier_signature" 
                                       placeholder="اسم ممثل المورد">
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="ملاحظات إضافية..."></textarea>
                    </div>

                    <!-- بنود الفاتورة -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-primary mb-0">
                                <i class="fas fa-list me-1"></i>
                                بنود الفاتورة
                            </h6>
                            <button type="button" class="btn btn-sm btn-success" onclick="addInvoiceItem()">
                                <i class="fas fa-plus me-1"></i>
                                إضافة بند
                            </button>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 40%">المنتج *</th>
                                        <th style="width: 20%">الكمية *</th>
                                        <th style="width: 30%">ملاحظات</th>
                                        <th style="width: 10%">إجراء</th>
                                    </tr>
                                </thead>
                                <tbody id="itemsTableBody">
                                    <!-- سيتم إضافة البنود هنا ديناميكياً -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i>
                        حفظ الفاتورة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة اختيار طلب الشراء -->
<div class="modal fade" id="purchaseOrderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">اختيار طلب شراء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>رقم الطلب</th>
                                <th>التاريخ</th>
                                <th>المورد</th>
                                <th>عدد البنود</th>
                                <th>الحالة</th>
                                <th>إجراء</th>
                            </tr>
                        </thead>
                        <tbody id="purchaseOrdersTableBody">
                            <!-- سيتم تحميل طلبات الشراء هنا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let itemCounter = 0;
let allProducts = [];

// تحميل المنتجات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadProducts();
    
    // تعيين تاريخ اليوم كافتراضي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('invoice_date').value = today;
    
    // إضافة بند فارغ افتراضي
    addInvoiceItem();
});

// تحميل قائمة المنتجات
function loadProducts() {
    fetch('/api/products')
        .then(response => response.json())
        .then(data => {
            allProducts = data;
        })
        .catch(error => {
            console.error('Error loading products:', error);
        });
}

// إعادة تعيين نموذج الفاتورة
function resetInvoiceForm() {
    document.getElementById('invoiceModalTitle').textContent = 'إضافة فاتورة إدخال';
    document.getElementById('invoiceForm').reset();
    document.getElementById('invoice_id').value = '';
    document.getElementById('itemsTableBody').innerHTML = '';
    itemCounter = 0;

    // تعيين تاريخ اليوم
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('invoice_date').value = today;

    // إضافة بند فارغ
    addInvoiceItem();
}

// إضافة بند جديد للفاتورة
function addInvoiceItem() {
    itemCounter++;
    const tbody = document.getElementById('itemsTableBody');
    const row = document.createElement('tr');

    // إنشاء قائمة المنتجات
    let productOptions = '<option value="">اختر المنتج</option>';
    allProducts.forEach(product => {
        productOptions += `<option value="${product.id}" data-name="${product.name}" data-unit="${product.unit_name || ''}">${product.name} (${product.code})</option>`;
    });

    row.innerHTML = `
        <td>
            <select class="form-control" name="product_id_${itemCounter}" onchange="updateProductInfo(this, ${itemCounter})" required>
                ${productOptions}
            </select>
        </td>
        <td>
            <input type="number" class="form-control" name="quantity_${itemCounter}"
                   placeholder="الكمية" step="0.01" min="0" required>
        </td>
        <td>
            <input type="text" class="form-control" name="notes_${itemCounter}"
                   placeholder="ملاحظات">
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeInvoiceItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(row);
}

// حذف بند من الفاتورة
function removeInvoiceItem(button) {
    const row = button.closest('tr');
    row.remove();
}

// تحديث معلومات المنتج عند الاختيار
function updateProductInfo(selectElement, counter) {
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const productName = selectedOption.getAttribute('data-name');
    const productUnit = selectedOption.getAttribute('data-unit');

    // يمكن إضافة المزيد من التحديثات هنا إذا لزم الأمر
}

// تحميل طلبات الشراء
function loadFromPurchaseOrder() {
    fetch('/api/purchase_orders?status=completed')
        .then(response => response.json())
        .then(data => {
            const tbody = document.getElementById('purchaseOrdersTableBody');
            tbody.innerHTML = '';

            data.forEach(order => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${order.order_number}</td>
                    <td>${order.order_date || '-'}</td>
                    <td>${order.supplier_name || '-'}</td>
                    <td><span class="badge bg-secondary">${order.items.length} بند</span></td>
                    <td><span class="badge bg-success">مكتمل</span></td>
                    <td>
                        <button type="button" class="btn btn-sm btn-primary"
                                onclick="selectPurchaseOrder(${order.id})">
                            <i class="fas fa-check me-1"></i>اختيار
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            new bootstrap.Modal(document.getElementById('purchaseOrderModal')).show();
        })
        .catch(error => {
            console.error('Error loading purchase orders:', error);
            alert('حدث خطأ في تحميل طلبات الشراء');
        });
}

// اختيار طلب شراء وتعبئة الفاتورة
function selectPurchaseOrder(orderId) {
    fetch(`/api/purchase_orders/${orderId}`)
        .then(response => response.json())
        .then(data => {
            // إغلاق نافذة اختيار طلب الشراء
            bootstrap.Modal.getInstance(document.getElementById('purchaseOrderModal')).hide();

            // فتح نافذة إضافة الفاتورة
            new bootstrap.Modal(document.getElementById('invoiceModal')).show();

            // تعبئة البيانات الأساسية
            document.getElementById('supplier_id').value = data.supplier_id;
            document.getElementById('warehouse_manager').value = data.warehouse_manager || '';

            // توليد رقم فاتورة تلقائي
            const today = new Date();
            const invoiceNumber = `INV-${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}-${Date.now().toString().slice(-4)}`;
            document.getElementById('invoice_number').value = invoiceNumber;

            // مسح البنود الموجودة
            document.getElementById('itemsTableBody').innerHTML = '';
            itemCounter = 0;

            // إضافة بنود طلب الشراء
            data.items.forEach(item => {
                itemCounter++;
                const tbody = document.getElementById('itemsTableBody');
                const row = document.createElement('tr');

                // البحث عن المنتج في قائمة المنتجات
                const product = allProducts.find(p => p.name === item.product_name);

                let productOptions = '<option value="">اختر المنتج</option>';
                allProducts.forEach(p => {
                    const selected = p.id === (product ? product.id : '') ? 'selected' : '';
                    productOptions += `<option value="${p.id}" data-name="${p.name}" data-unit="${p.unit_name || ''}" ${selected}>${p.name} (${p.code})</option>`;
                });

                row.innerHTML = `
                    <td>
                        <select class="form-control" name="product_id_${itemCounter}" onchange="updateProductInfo(this, ${itemCounter})" required>
                            ${productOptions}
                        </select>
                    </td>
                    <td>
                        <input type="number" class="form-control" name="quantity_${itemCounter}"
                               value="${item.quantity}" placeholder="الكمية" step="0.01" min="0" required>
                    </td>
                    <td>
                        <input type="text" class="form-control" name="notes_${itemCounter}"
                               value="${item.notes || ''}" placeholder="ملاحظات">
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeInvoiceItem(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // إضافة بند فارغ إضافي
            addInvoiceItem();
        })
        .catch(error => {
            console.error('Error loading purchase order:', error);
            alert('حدث خطأ في تحميل طلب الشراء');
        });
}

// عرض تفاصيل الفاتورة
function viewInvoice(invoiceId) {
    fetch(`/api/entry_invoices/${invoiceId}`)
        .then(response => response.json())
        .then(data => {
            const modalHtml = `
                <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تفاصيل فاتورة الإدخال - ${data.invoice_number}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>رقم الفاتورة:</strong> ${data.invoice_number}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>تاريخ الفاتورة:</strong> ${data.invoice_date || '-'}
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>المورد:</strong> ${data.supplier_name || '-'}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>مدير المستودع:</strong> ${data.warehouse_manager || '-'}
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>توقيع المورد:</strong> ${data.supplier_signature || '-'}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>تاريخ الإنشاء:</strong> ${data.created_at || '-'}
                                    </div>
                                </div>
                                ${data.notes ? `<div class="mb-3"><strong>ملاحظات:</strong> ${data.notes}</div>` : ''}

                                <h6 class="mt-4 mb-3">بنود الفاتورة:</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكمية</th>
                                                <th>الوحدة</th>
                                                <th>ملاحظات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${data.items.map(item => `
                                                <tr>
                                                    <td>${item.product_name}</td>
                                                    <td>${item.quantity}</td>
                                                    <td>${item.product_unit || '-'}</td>
                                                    <td>${item.notes || '-'}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                <button type="button" class="btn btn-primary" onclick="printInvoice(${invoiceId})">
                                    <i class="fas fa-print me-1"></i>طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            const existingModal = document.getElementById('viewInvoiceModal');
            if (existingModal) {
                existingModal.remove();
            }

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            new bootstrap.Modal(document.getElementById('viewInvoiceModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحميل تفاصيل الفاتورة');
        });
}

// تعديل الفاتورة
function editInvoice(invoiceId) {
    fetch(`/api/entry_invoices/${invoiceId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('invoiceModalTitle').textContent = 'تعديل فاتورة الإدخال';
            document.getElementById('invoice_id').value = data.id;
            document.getElementById('invoice_number').value = data.invoice_number;
            document.getElementById('invoice_date').value = data.invoice_date;
            document.getElementById('supplier_id').value = data.supplier_id;
            document.getElementById('warehouse_manager').value = data.warehouse_manager || '';
            document.getElementById('supplier_signature').value = data.supplier_signature || '';
            document.getElementById('notes').value = data.notes || '';

            // مسح جدول البنود
            document.getElementById('itemsTableBody').innerHTML = '';
            itemCounter = 0;

            // إضافة البنود الموجودة
            data.items.forEach(item => {
                itemCounter++;
                const tbody = document.getElementById('itemsTableBody');
                const row = document.createElement('tr');

                let productOptions = '<option value="">اختر المنتج</option>';
                allProducts.forEach(product => {
                    const selected = product.id === item.product_id ? 'selected' : '';
                    productOptions += `<option value="${product.id}" data-name="${product.name}" data-unit="${product.unit_name || ''}" ${selected}>${product.name} (${product.code})</option>`;
                });

                row.innerHTML = `
                    <td>
                        <select class="form-control" name="product_id_${itemCounter}" onchange="updateProductInfo(this, ${itemCounter})" required>
                            ${productOptions}
                        </select>
                    </td>
                    <td>
                        <input type="number" class="form-control" name="quantity_${itemCounter}"
                               value="${item.quantity}" placeholder="الكمية" step="0.01" min="0" required>
                    </td>
                    <td>
                        <input type="text" class="form-control" name="notes_${itemCounter}"
                               value="${item.notes || ''}" placeholder="ملاحظات">
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeInvoiceItem(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // إضافة بند فارغ إضافي
            addInvoiceItem();

            new bootstrap.Modal(document.getElementById('invoiceModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحميل بيانات الفاتورة');
        });
}

// طباعة الفاتورة
function printInvoice(invoiceId) {
    window.open(`/entry_invoices/${invoiceId}/print`, '_blank');
}

// حذف الفاتورة
function deleteInvoice(invoiceId) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
        fetch(`/api/entry_invoices/${invoiceId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في حذف الفاتورة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في حذف الفاتورة');
        });
    }
}

// البحث والفلترة
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const supplierFilter = document.getElementById('supplierFilter');
    const dateFilter = document.getElementById('dateFilter');

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedSupplier = supplierFilter.value;
        const selectedDate = dateFilter.value;
        const rows = document.querySelectorAll('#invoicesTableBody tr');

        rows.forEach(row => {
            const invoiceNumber = row.cells[0]?.textContent.toLowerCase() || '';
            const supplierName = row.cells[2]?.textContent.toLowerCase() || '';
            const invoiceDate = row.cells[1]?.textContent || '';

            const matchesSearch = invoiceNumber.includes(searchTerm) || supplierName.includes(searchTerm);
            const matchesSupplier = !selectedSupplier || row.cells[2]?.textContent.includes(selectedSupplier);
            const matchesDate = !selectedDate || invoiceDate.includes(selectedDate);

            if (matchesSearch && matchesSupplier && matchesDate) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    searchInput.addEventListener('input', filterTable);
    supplierFilter.addEventListener('change', filterTable);
    dateFilter.addEventListener('change', filterTable);
});
</script>
{% endblock %}
