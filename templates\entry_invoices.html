{% extends "base.html" %}

{% block title %}فواتير الإدخال{% endblock %}

{% block extra_css %}
<style>
    /* تأثيرات الباركود */
    .form-control.is-loading {
        background-image: url("data:image/svg+xml,%3csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%236c757d' fill-opacity='0.8'%3e%3cpath d='m10 15c-2.8 0-5-2.2-5-5s2.2-5 5-5 5 2.2 5 5-2.2 5-5 5zm0-8c-1.7 0-3 1.3-3 3s1.3 3 3 3 3-1.3 3-3-1.3-3-3-3z'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 20px 20px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .barcode-input-container {
        position: relative;
    }

    .barcode-input-container .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn-barcode {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-barcode:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .table th {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        vertical-align: middle;
        text-align: center;
    }

    .table tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
        transition: background-color 0.2s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-file-import me-2"></i>
                    فواتير الإدخال
                </h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#invoiceModal" onclick="resetInvoiceForm()">
                        <i class="fas fa-plus me-1"></i>
                        إضافة فاتورة إدخال
                    </button>
                    <button type="button" class="btn btn-info" onclick="loadFromPurchaseOrder()">
                        <i class="fas fa-shopping-cart me-1"></i>
                        من طلب شراء
                    </button>
                </div>
            </div>

            <!-- شريط البحث والفلترة -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="البحث برقم الفاتورة أو المورد...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="supplierFilter">
                                <option value="">جميع الموردين</option>
                                {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="dateFilter" placeholder="تاريخ الفاتورة">
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول فواتير الإدخال -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>تاريخ الفاتورة</th>
                                    <th>المورد</th>
                                    <th>عدد البنود</th>
                                    <th>مدير المستودع</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="invoicesTableBody">
                                {% for invoice in invoices %}
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ invoice.invoice_number }}</strong>
                                    </td>
                                    <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else '-' }}</td>
                                    <td>
                                        <i class="fas fa-truck text-info me-1"></i>
                                        {{ invoice.supplier_ref.name if invoice.supplier_ref else '-' }}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ invoice.items|length }} بند</span>
                                    </td>
                                    <td>{{ invoice.warehouse_manager or '-' }}</td>
                                    <td>{{ invoice.created_at.strftime('%Y-%m-%d %H:%M') if invoice.created_at else '-' }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-info" 
                                                    onclick="viewInvoice({{ invoice.id }})" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-warning" 
                                                    onclick="editInvoice({{ invoice.id }})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-success" 
                                                    onclick="printInvoice({{ invoice.id }})" title="طباعة">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteInvoice({{ invoice.id }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>
                                        لا توجد فواتير إدخال حتى الآن
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة/تعديل فاتورة الإدخال -->
<div class="modal fade" id="invoiceModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="invoiceModalTitle">إضافة فاتورة إدخال</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/entry_invoices" id="invoiceForm">
                <div class="modal-body">
                    <input type="hidden" id="invoice_id" name="invoice_id">
                    
                    <!-- معلومات الفاتورة الأساسية -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="invoice_number" class="form-label">رقم الفاتورة *</label>
                                <input type="text" class="form-control" id="invoice_number" name="invoice_number" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="invoice_date" class="form-label">تاريخ الفاتورة *</label>
                                <input type="date" class="form-control" id="invoice_date" name="invoice_date" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="supplier_id" class="form-label">المورد *</label>
                                <select class="form-select" id="supplier_id" name="supplier_id" required>
                                    <option value="">اختر المورد</option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="warehouse_manager" class="form-label">مدير المستودع</label>
                                <input type="text" class="form-control" id="warehouse_manager" name="warehouse_manager" 
                                       placeholder="اسم مدير المستودع">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="supplier_signature" class="form-label">توقيع المورد</label>
                                <input type="text" class="form-control" id="supplier_signature" name="supplier_signature" 
                                       placeholder="اسم ممثل المورد">
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="ملاحظات إضافية..."></textarea>
                    </div>

                    <!-- بنود الفاتورة -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-primary mb-0">
                                <i class="fas fa-list me-1"></i>
                                بنود الفاتورة
                            </h6>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-success" onclick="addInvoiceItem()">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة بند
                                </button>
                                <button type="button" class="btn btn-sm btn-info" onclick="focusBarcodeInput()">
                                    <i class="fas fa-barcode me-1"></i>
                                    مسح باركود
                                </button>
                            </div>
                        </div>

                        <!-- حقل البحث بالباركود -->
                        <div class="card mb-3">
                            <div class="card-body">
                                <div class="row align-items-end">
                                    <div class="col-md-8">
                                        <label for="barcodeInput" class="form-label">
                                            <i class="fas fa-barcode me-1"></i>
                                            مسح أو إدخال الباركود
                                        </label>
                                        <input type="text" class="form-control form-control-lg" id="barcodeInput"
                                               placeholder="امسح الباركود أو اكتبه هنا..."
                                               onkeypress="handleBarcodeInput(event)">
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-primary btn-lg w-100 btn-barcode" onclick="searchByBarcode()">
                                            <i class="fas fa-search me-1"></i>
                                            بحث
                                        </button>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        اضغط Enter بعد مسح الباركود أو استخدم زر البحث
                                    </small>
                                    <small class="text-success" id="barcodeStatus" style="display: none;">
                                        <i class="fas fa-check-circle me-1"></i>
                                        تم العثور على المنتج!
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 12%">الباركود</th>
                                        <th style="width: 18%">المنتج</th>
                                        <th style="width: 10%">وحدة القياس</th>
                                        <th style="width: 10%">حالة المادة</th>
                                        <th style="width: 12%">التصنيف</th>
                                        <th style="width: 12%">المستودع</th>
                                        <th style="width: 8%">الكمية *</th>
                                        <th style="width: 12%">ملاحظات</th>
                                        <th style="width: 6%">إجراء</th>
                                    </tr>
                                </thead>
                                <tbody id="itemsTableBody">
                                    <!-- سيتم إضافة البنود هنا ديناميكياً -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success" id="saveInvoiceBtn">
                        <i class="fas fa-save me-1"></i>
                        حفظ الفاتورة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة اختيار طلب الشراء -->
<div class="modal fade" id="purchaseOrderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">اختيار طلب شراء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>رقم الطلب</th>
                                <th>التاريخ</th>
                                <th>المورد</th>
                                <th>عدد البنود</th>
                                <th>الحالة</th>
                                <th>إجراء</th>
                            </tr>
                        </thead>
                        <tbody id="purchaseOrdersTableBody">
                            <!-- سيتم تحميل طلبات الشراء هنا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة منتج جديد -->
<div class="modal fade" id="newProductModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة منتج جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="newProductForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_product_code" class="form-label">كود المنتج (الباركود) *</label>
                                <input type="text" class="form-control" id="new_product_code" name="code" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_product_name" class="form-label">اسم المنتج *</label>
                                <input type="text" class="form-control" id="new_product_name" name="name" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_product_category" class="form-label">التصنيف *</label>
                                <select class="form-select" id="new_product_category" name="category_id" required>
                                    <option value="">اختر التصنيف</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_product_unit" class="form-label">وحدة القياس *</label>
                                <select class="form-select" id="new_product_unit" name="unit_id" required>
                                    <option value="">اختر وحدة القياس</option>
                                    {% for unit in units %}
                                    <option value="{{ unit.id }}">{{ unit.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_product_condition" class="form-label">حالة المادة *</label>
                                <select class="form-select" id="new_product_condition" name="condition_id" required>
                                    <option value="">اختر حالة المادة</option>
                                    {% for condition in conditions %}
                                    <option value="{{ condition.id }}">{{ condition.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_product_warehouse" class="form-label">المستودع *</label>
                                <select class="form-select" id="new_product_warehouse" name="warehouse_id" required>
                                    <option value="">اختر المستودع</option>
                                    {% for warehouse in warehouses %}
                                    <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="new_product_description" class="form-label">وصف المنتج</label>
                        <textarea class="form-control" id="new_product_description" name="description" rows="3"
                                  placeholder="وصف تفصيلي للمنتج..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="new_product_notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="new_product_notes" name="notes" rows="2"
                                  placeholder="ملاحظات إضافية..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i>
                        حفظ المنتج وإضافته للفاتورة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let itemCounter = 0;
let allProducts = [];

// تحميل المنتجات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadProducts();
    
    // تعيين تاريخ اليوم كافتراضي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('invoice_date').value = today;
    
    // إضافة بند فارغ افتراضي
    addInvoiceItem();
});

// تحميل قائمة المنتجات
function loadProducts() {
    fetch('/api/products')
        .then(response => response.json())
        .then(data => {
            allProducts = data;
        })
        .catch(error => {
            console.error('Error loading products:', error);
        });
}

// إعادة تعيين نموذج الفاتورة
function resetInvoiceForm() {
    document.getElementById('invoiceModalTitle').textContent = 'إضافة فاتورة إدخال';
    document.getElementById('invoiceForm').reset();
    document.getElementById('invoice_id').value = '';
    document.getElementById('itemsTableBody').innerHTML = '';
    itemCounter = 0;

    // تعيين تاريخ اليوم
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('invoice_date').value = today;

    // إضافة بند فارغ
    addInvoiceItem();
}

// إضافة بند جديد للفاتورة
function addInvoiceItem(productData = null) {
    itemCounter++;
    const tbody = document.getElementById('itemsTableBody');
    const row = document.createElement('tr');

    // تحديد البيانات الافتراضية
    const barcode = productData ? productData.code : '';
    const productName = productData ? productData.name : '';
    const unitName = productData ? productData.unit_name : '';
    const conditionName = productData ? productData.condition_name : '';
    const categoryName = productData ? productData.category_name : '';
    const warehouseName = productData ? productData.warehouse_name : '';
    const productId = productData ? productData.id : '';

    row.innerHTML = `
        <td>
            <input type="text" class="form-control" name="barcode_${itemCounter}"
                   value="${barcode}" placeholder="الباركود" readonly>
            <input type="hidden" name="product_id_${itemCounter}" value="${productId}">
        </td>
        <td>
            <input type="text" class="form-control" name="product_name_${itemCounter}"
                   value="${productName}" placeholder="اسم المنتج" readonly>
        </td>
        <td>
            <input type="text" class="form-control" name="unit_name_${itemCounter}"
                   value="${unitName}" placeholder="الوحدة" readonly>
        </td>
        <td>
            <input type="text" class="form-control" name="condition_name_${itemCounter}"
                   value="${conditionName}" placeholder="الحالة" readonly>
        </td>
        <td>
            <input type="text" class="form-control" name="category_name_${itemCounter}"
                   value="${categoryName}" placeholder="التصنيف" readonly>
        </td>
        <td>
            <input type="text" class="form-control" name="warehouse_name_${itemCounter}"
                   value="${warehouseName}" placeholder="المستودع" readonly>
        </td>
        <td>
            <input type="number" class="form-control" name="quantity_${itemCounter}"
                   placeholder="الكمية" step="0.01" min="0" required>
        </td>
        <td>
            <input type="text" class="form-control" name="notes_${itemCounter}"
                   placeholder="ملاحظات">
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeInvoiceItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(row);

    // التركيز على حقل الكمية إذا كان المنتج محدد
    if (productData) {
        const quantityInput = row.querySelector(`input[name="quantity_${itemCounter}"]`);
        quantityInput.focus();
    }
}

// البحث بالباركود
function searchByBarcode() {
    const barcodeInput = document.getElementById('barcodeInput');
    const barcodeStatus = document.getElementById('barcodeStatus');
    const barcode = barcodeInput.value.trim();

    if (!barcode) {
        showBarcodeMessage('يرجى إدخال الباركود', 'error');
        return;
    }

    // إضافة تأثير التحميل
    barcodeInput.classList.add('is-loading');

    // البحث في قائمة المنتجات
    const product = allProducts.find(p => p.code === barcode);

    setTimeout(() => {
        barcodeInput.classList.remove('is-loading');

        if (product) {
            // عرض رسالة النجاح
            showBarcodeMessage('تم العثور على المنتج!', 'success');

            // إضافة المنتج للفاتورة
            addInvoiceItem(product);

            // مسح حقل الباركود
            barcodeInput.value = '';

            // التركيز على حقل الباركود للمنتج التالي
            setTimeout(() => {
                barcodeInput.focus();
                hideBarcodeMessage();
            }, 1500);
        } else {
            // عرض رسالة عدم وجود المنتج
            showBarcodeMessage('المنتج غير موجود', 'error');

            // عرض خيار إضافة منتج جديد
            setTimeout(() => {
                if (confirm(`المنتج بالباركود "${barcode}" غير موجود.\nهل تريد إضافة منتج جديد؟`)) {
                    showNewProductModal(barcode);
                    hideBarcodeMessage();
                } else {
                    barcodeInput.select();
                    hideBarcodeMessage();
                }
            }, 1000);
        }
    }, 500); // تأخير قصير لإظهار تأثير التحميل
}

// عرض رسائل حالة الباركود
function showBarcodeMessage(message, type) {
    const barcodeStatus = document.getElementById('barcodeStatus');
    const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';
    const colorClass = type === 'success' ? 'text-success' : 'text-danger';

    barcodeStatus.innerHTML = `<i class="${icon} me-1"></i>${message}`;
    barcodeStatus.className = `small ${colorClass}`;
    barcodeStatus.style.display = 'block';
}

// إخفاء رسائل حالة الباركود
function hideBarcodeMessage() {
    const barcodeStatus = document.getElementById('barcodeStatus');
    barcodeStatus.style.display = 'none';
}

// معالجة إدخال الباركود بالضغط على Enter
function handleBarcodeInput(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        searchByBarcode();
    }
}

// التركيز على حقل الباركود
function focusBarcodeInput() {
    document.getElementById('barcodeInput').focus();
}

// عرض نافذة إضافة منتج جديد
function showNewProductModal(barcode = '') {
    document.getElementById('new_product_code').value = barcode;
    new bootstrap.Modal(document.getElementById('newProductModal')).show();
}

// حذف بند من الفاتورة
function removeInvoiceItem(button) {
    const row = button.closest('tr');
    row.remove();
}

// تحديث معلومات المنتج عند الاختيار
function updateProductInfo(selectElement, counter) {
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const productName = selectedOption.getAttribute('data-name');
    const productUnit = selectedOption.getAttribute('data-unit');

    // يمكن إضافة المزيد من التحديثات هنا إذا لزم الأمر
}

// تحميل طلبات الشراء
function loadFromPurchaseOrder() {
    fetch('/api/purchase_orders?status=completed')
        .then(response => response.json())
        .then(data => {
            const tbody = document.getElementById('purchaseOrdersTableBody');
            tbody.innerHTML = '';

            data.forEach(order => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${order.order_number}</td>
                    <td>${order.order_date || '-'}</td>
                    <td>${order.supplier_name || '-'}</td>
                    <td><span class="badge bg-secondary">${order.items.length} بند</span></td>
                    <td><span class="badge bg-success">مكتمل</span></td>
                    <td>
                        <button type="button" class="btn btn-sm btn-primary"
                                onclick="selectPurchaseOrder(${order.id})">
                            <i class="fas fa-check me-1"></i>اختيار
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            new bootstrap.Modal(document.getElementById('purchaseOrderModal')).show();
        })
        .catch(error => {
            console.error('Error loading purchase orders:', error);
            alert('حدث خطأ في تحميل طلبات الشراء');
        });
}

// اختيار طلب شراء وتعبئة الفاتورة
function selectPurchaseOrder(orderId) {
    fetch(`/api/purchase_orders/${orderId}`)
        .then(response => response.json())
        .then(data => {
            // إغلاق نافذة اختيار طلب الشراء
            bootstrap.Modal.getInstance(document.getElementById('purchaseOrderModal')).hide();

            // فتح نافذة إضافة الفاتورة
            new bootstrap.Modal(document.getElementById('invoiceModal')).show();

            // تعبئة البيانات الأساسية
            document.getElementById('supplier_id').value = data.supplier_id;
            document.getElementById('warehouse_manager').value = data.warehouse_manager || '';

            // توليد رقم فاتورة تلقائي
            const today = new Date();
            const invoiceNumber = `INV-${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}-${Date.now().toString().slice(-4)}`;
            document.getElementById('invoice_number').value = invoiceNumber;

            // مسح البنود الموجودة
            document.getElementById('itemsTableBody').innerHTML = '';
            itemCounter = 0;

            // إضافة بنود طلب الشراء
            data.items.forEach(item => {
                itemCounter++;
                const tbody = document.getElementById('itemsTableBody');
                const row = document.createElement('tr');

                // البحث عن المنتج في قائمة المنتجات
                const product = allProducts.find(p => p.name === item.product_name);

                let productOptions = '<option value="">اختر المنتج</option>';
                allProducts.forEach(p => {
                    const selected = p.id === (product ? product.id : '') ? 'selected' : '';
                    productOptions += `<option value="${p.id}" data-name="${p.name}" data-unit="${p.unit_name || ''}" ${selected}>${p.name} (${p.code})</option>`;
                });

                row.innerHTML = `
                    <td>
                        <select class="form-control" name="product_id_${itemCounter}" onchange="updateProductInfo(this, ${itemCounter})" required>
                            ${productOptions}
                        </select>
                    </td>
                    <td>
                        <input type="number" class="form-control" name="quantity_${itemCounter}"
                               value="${item.quantity}" placeholder="الكمية" step="0.01" min="0" required>
                    </td>
                    <td>
                        <input type="text" class="form-control" name="notes_${itemCounter}"
                               value="${item.notes || ''}" placeholder="ملاحظات">
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeInvoiceItem(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // إضافة بند فارغ إضافي
            addInvoiceItem();
        })
        .catch(error => {
            console.error('Error loading purchase order:', error);
            alert('حدث خطأ في تحميل طلب الشراء');
        });
}

// عرض تفاصيل الفاتورة
function viewInvoice(invoiceId) {
    fetch(`/api/entry_invoices/${invoiceId}`)
        .then(response => response.json())
        .then(data => {
            const modalHtml = `
                <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تفاصيل فاتورة الإدخال - ${data.invoice_number}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>رقم الفاتورة:</strong> ${data.invoice_number}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>تاريخ الفاتورة:</strong> ${data.invoice_date || '-'}
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>المورد:</strong> ${data.supplier_name || '-'}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>مدير المستودع:</strong> ${data.warehouse_manager || '-'}
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>توقيع المورد:</strong> ${data.supplier_signature || '-'}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>تاريخ الإنشاء:</strong> ${data.created_at || '-'}
                                    </div>
                                </div>
                                ${data.notes ? `<div class="mb-3"><strong>ملاحظات:</strong> ${data.notes}</div>` : ''}

                                <h6 class="mt-4 mb-3">بنود الفاتورة:</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكمية</th>
                                                <th>الوحدة</th>
                                                <th>ملاحظات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${data.items.map(item => `
                                                <tr>
                                                    <td>${item.product_name}</td>
                                                    <td>${item.quantity}</td>
                                                    <td>${item.product_unit || '-'}</td>
                                                    <td>${item.notes || '-'}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                <button type="button" class="btn btn-primary" onclick="printInvoice(${invoiceId})">
                                    <i class="fas fa-print me-1"></i>طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            const existingModal = document.getElementById('viewInvoiceModal');
            if (existingModal) {
                existingModal.remove();
            }

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            new bootstrap.Modal(document.getElementById('viewInvoiceModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحميل تفاصيل الفاتورة');
        });
}

// تعديل الفاتورة
function editInvoice(invoiceId) {
    fetch(`/api/entry_invoices/${invoiceId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('invoiceModalTitle').textContent = 'تعديل فاتورة الإدخال';
            document.getElementById('invoice_id').value = data.id;
            document.getElementById('invoice_number').value = data.invoice_number;
            document.getElementById('invoice_date').value = data.invoice_date;
            document.getElementById('supplier_id').value = data.supplier_id;
            document.getElementById('warehouse_manager').value = data.warehouse_manager || '';
            document.getElementById('supplier_signature').value = data.supplier_signature || '';
            document.getElementById('notes').value = data.notes || '';

            // مسح جدول البنود
            document.getElementById('itemsTableBody').innerHTML = '';
            itemCounter = 0;

            // إضافة البنود الموجودة
            data.items.forEach(item => {
                itemCounter++;
                const tbody = document.getElementById('itemsTableBody');
                const row = document.createElement('tr');

                let productOptions = '<option value="">اختر المنتج</option>';
                allProducts.forEach(product => {
                    const selected = product.id === item.product_id ? 'selected' : '';
                    productOptions += `<option value="${product.id}" data-name="${product.name}" data-unit="${product.unit_name || ''}" ${selected}>${product.name} (${product.code})</option>`;
                });

                row.innerHTML = `
                    <td>
                        <select class="form-control" name="product_id_${itemCounter}" onchange="updateProductInfo(this, ${itemCounter})" required>
                            ${productOptions}
                        </select>
                    </td>
                    <td>
                        <input type="number" class="form-control" name="quantity_${itemCounter}"
                               value="${item.quantity}" placeholder="الكمية" step="0.01" min="0" required>
                    </td>
                    <td>
                        <input type="text" class="form-control" name="notes_${itemCounter}"
                               value="${item.notes || ''}" placeholder="ملاحظات">
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeInvoiceItem(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // إضافة بند فارغ إضافي
            addInvoiceItem();

            new bootstrap.Modal(document.getElementById('invoiceModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحميل بيانات الفاتورة');
        });
}

// طباعة الفاتورة
function printInvoice(invoiceId) {
    window.open(`/entry_invoices/${invoiceId}/print`, '_blank');
}

// حذف الفاتورة
function deleteInvoice(invoiceId) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
        fetch(`/api/entry_invoices/${invoiceId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في حذف الفاتورة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في حذف الفاتورة');
        });
    }
}

// معالج النموذج الرئيسي
document.addEventListener('DOMContentLoaded', function() {
    console.log('JavaScript تم تحميله بنجاح');

    const invoiceForm = document.getElementById('invoiceForm');
    if (invoiceForm) {
        invoiceForm.addEventListener('submit', function(e) {
            e.preventDefault(); // منع الإرسال العادي

            console.log('=== معالجة إرسال النموذج ===');

            const itemsTable = document.getElementById('itemsTableBody');
            const rows = itemsTable.querySelectorAll('tr');

            console.log('عدد البنود في الجدول:', rows.length);

            if (rows.length === 0) {
                alert('يرجى إضافة بند واحد على الأقل');
                return;
            }

            // إضافة حقول مخفية للبنود قبل الإرسال
            rows.forEach((row, index) => {
                const counter = index + 1;

                // البحث عن الحقول في الصف
                const productIdInput = row.querySelector(`input[name="product_id_${counter}"]`);
                const quantityInput = row.querySelector(`input[name="quantity_${counter}"]`);
                const notesInput = row.querySelector(`input[name="notes_${counter}"]`);

                if (productIdInput && quantityInput) {
                    console.log(`البند ${counter}: product_id=${productIdInput.value}, quantity=${quantityInput.value}`);

                    // التأكد من أن الحقول موجودة في النموذج
                    if (!this.querySelector(`input[name="product_id_${counter}"]`)) {
                        const hiddenProductId = document.createElement('input');
                        hiddenProductId.type = 'hidden';
                        hiddenProductId.name = `product_id_${counter}`;
                        hiddenProductId.value = productIdInput.value;
                        this.appendChild(hiddenProductId);
                    }

                    if (!this.querySelector(`input[name="quantity_${counter}"]`)) {
                        const hiddenQuantity = document.createElement('input');
                        hiddenQuantity.type = 'hidden';
                        hiddenQuantity.name = `quantity_${counter}`;
                        hiddenQuantity.value = quantityInput.value;
                        this.appendChild(hiddenQuantity);
                    }

                    if (notesInput && !this.querySelector(`input[name="notes_${counter}"]`)) {
                        const hiddenNotes = document.createElement('input');
                        hiddenNotes.type = 'hidden';
                        hiddenNotes.name = `notes_${counter}`;
                        hiddenNotes.value = notesInput.value || '';
                        this.appendChild(hiddenNotes);
                    }
                }
            });

            console.log('إرسال النموذج...');
            this.submit(); // إرسال النموذج بالطريقة العادية
        });
    }

    // معالج نموذج إضافة المنتج الجديد
    const newProductForm = document.getElementById('newProductForm');
    if (newProductForm) {
        newProductForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const productData = {
                code: formData.get('code'),
                name: formData.get('name'),
                category_id: formData.get('category_id'),
                unit_id: formData.get('unit_id'),
                condition_id: formData.get('condition_id'),
                warehouse_id: formData.get('warehouse_id'),
                description: formData.get('description'),
                notes: formData.get('notes')
            };

            // إرسال البيانات لإضافة المنتج
            fetch('/api/products', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(productData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إضافة المنتج الجديد لقائمة المنتجات
                    allProducts.push(data.product);

                    // إضافة المنتج للفاتورة
                    addInvoiceItem(data.product);

                    // إغلاق النافذة ومسح النموذج
                    bootstrap.Modal.getInstance(document.getElementById('newProductModal')).hide();
                    newProductForm.reset();

                    // مسح حقل الباركود والتركيز عليه
                    document.getElementById('barcodeInput').value = '';
                    document.getElementById('barcodeInput').focus();

                    alert('تم إضافة المنتج بنجاح!');
                } else {
                    alert('حدث خطأ في إضافة المنتج: ' + (data.message || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في إضافة المنتج');
            });
        });
    }
});

// البحث والفلترة
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const supplierFilter = document.getElementById('supplierFilter');
    const dateFilter = document.getElementById('dateFilter');

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedSupplier = supplierFilter.value;
        const selectedDate = dateFilter.value;
        const rows = document.querySelectorAll('#invoicesTableBody tr');

        rows.forEach(row => {
            const invoiceNumber = row.cells[0]?.textContent.toLowerCase() || '';
            const supplierName = row.cells[2]?.textContent.toLowerCase() || '';
            const invoiceDate = row.cells[1]?.textContent || '';

            const matchesSearch = invoiceNumber.includes(searchTerm) || supplierName.includes(searchTerm);
            const matchesSupplier = !selectedSupplier || row.cells[2]?.textContent.includes(selectedSupplier);
            const matchesDate = !selectedDate || invoiceDate.includes(selectedDate);

            if (matchesSearch && matchesSupplier && matchesDate) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    if (searchInput) searchInput.addEventListener('input', filterTable);
    if (supplierFilter) supplierFilter.addEventListener('change', filterTable);
    if (dateFilter) dateFilter.addEventListener('change', filterTable);
});
</script>
{% endblock %}
