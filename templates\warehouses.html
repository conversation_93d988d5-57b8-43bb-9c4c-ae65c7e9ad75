{% extends 'index.html' %}
{% block content %}
<div class="container mt-5">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>إدارة المستودعات</h2>
        <a href="{{ url_for('add_warehouse') }}" class="btn btn-success">إضافة مستودع</a>
    </div>
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ category }}">{{ message }}</div>
        {% endfor %}
      {% endif %}
    {% endwith %}
    <table class="table table-bordered table-striped text-center">
        <thead class="table-dark">
            <tr>
                <th>#</th>
                <th>اسم المستودع</th>
                <th>الموقع</th>
                <th>الوصف</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for warehouse in warehouses %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>{{ warehouse.name }}</td>
                <td>{{ warehouse.location }}</td>
                <td>{{ warehouse.description }}</td>
                <td>
                    <a href="{{ url_for('show_warehouse', id=warehouse.id) }}" class="btn btn-info btn-sm">عرض</a>
                    <a href="{{ url_for('edit_warehouse', id=warehouse.id) }}" class="btn btn-primary btn-sm">تعديل</a>
                    <form action="{{ url_for('delete_warehouse', id=warehouse.id) }}" method="post" style="display:inline-block;">
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟')">حذف</button>
                    </form>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
