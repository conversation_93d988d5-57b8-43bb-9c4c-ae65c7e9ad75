{% extends "base.html" %}

{% block title %}إدارة المستودعات - نظام إدارة المستودعات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">إدارة المستودعات</h1>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#warehouseModal">
        <i class="fas fa-plus me-2"></i>
        إضافة مستودع جديد
    </button>
</div>

<!-- جدول المستودعات -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">
                    <i class="fas fa-warehouse me-2"></i>
                    قائمة المستودعات
                </h5>
            </div>
            <div class="col-auto">
                <div class="input-group">
                    <input type="text" class="form-control table-search" placeholder="البحث في المستودعات...">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if warehouses %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th class="sortable">اسم المستودع</th>
                        <th class="sortable">الموقع</th>
                        <th class="sortable">الوصف</th>
                        <th class="sortable">عدد المنتجات</th>
                        <th class="sortable">تاريخ الإنشاء</th>
                        <th width="200">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for warehouse in warehouses %}
                    <tr>
                        <td>
                            <strong>{{ warehouse.name }}</strong>
                        </td>
                        <td>{{ warehouse.location or '-' }}</td>
                        <td>{{ warehouse.description or '-' }}</td>
                        <td>
                            <span class="badge bg-primary">
                                {{ warehouse.products|length }}
                            </span>
                        </td>
                        <td>{{ warehouse.created_at.strftime('%Y/%m/%d') }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-info"
                                        onclick="viewWarehouse({{ warehouse.id }})"
                                        title="عرض المنتجات">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-warning"
                                        onclick="editWarehouse({{ warehouse.id }})"
                                        title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger delete-btn"
                                        onclick="deleteWarehouse({{ warehouse.id }})"
                                        data-item-name="{{ warehouse.name }}"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مستودعات</h5>
            <p class="text-muted">ابدأ بإضافة مستودع جديد</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#warehouseModal">
                <i class="fas fa-plus me-2"></i>
                إضافة مستودع جديد
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- نافذة إضافة/تعديل المستودع -->
<div class="modal fade" id="warehouseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="warehouseModalTitle">إضافة مستودع جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="warehouseForm" method="POST" action="{{ url_for('warehouses') }}">
                <div class="modal-body">
                    <input type="hidden" id="warehouse_id" name="warehouse_id">

                    <div class="mb-3">
                        <label for="warehouse_name" class="form-label">اسم المستودع *</label>
                        <input type="text" class="form-control" id="warehouse_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="warehouse_location" class="form-label">الموقع</label>
                        <input type="text" class="form-control" id="warehouse_location" name="location">
                    </div>

                    <div class="mb-3">
                        <label for="warehouse_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="warehouse_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentWarehouseId = null;

// إضافة مستودع جديد
function addWarehouse() {
    currentWarehouseId = null;
    document.getElementById('warehouseModalTitle').textContent = 'إضافة مستودع جديد';
    document.getElementById('warehouseForm').reset();
    document.getElementById('warehouse_id').value = '';
}

// تعديل مستودع
function editWarehouse(id) {
    currentWarehouseId = id;
    document.getElementById('warehouseModalTitle').textContent = 'تعديل المستودع';

    // جلب بيانات المستودع
    fetch(`/api/warehouses/${id}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('warehouse_id').value = data.id;
            document.getElementById('warehouse_name').value = data.name;
            document.getElementById('warehouse_location').value = data.location || '';
            document.getElementById('warehouse_description').value = data.description || '';

            new bootstrap.Modal(document.getElementById('warehouseModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في جلب بيانات المستودع');
        });
}

// عرض منتجات المستودع
function viewWarehouse(id) {
    alert('سيتم تطوير هذه الميزة قريباً');
}

// حذف مستودع
function deleteWarehouse(id) {
    if (confirm('هل أنت متأكد من حذف هذا المستودع؟ سيتم حذف جميع المنتجات المرتبطة به.')) {
        fetch(`/api/warehouses/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

// إعداد النافذة عند فتحها
document.getElementById('warehouseModal').addEventListener('show.bs.modal', function (event) {
    if (!currentWarehouseId) {
        addWarehouse();
    }
});
</script>
{% endblock %}