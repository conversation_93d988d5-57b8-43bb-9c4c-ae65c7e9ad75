{% extends "base.html" %}

{% block title %}الرئيسية - نظام إدارة المستودعات{% endblock %}
{% block page_title %}لوحة التحكم الرئيسية{% endblock %}

{% block content %}
<div class="row">
    <!-- إحصائيات سريعة -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-number">{{ stats.total_products }}</div>
            <div class="stats-label">
                <i class="fas fa-box me-2"></i>إجمالي المنتجات
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card success">
            <div class="stats-number">{{ stats.total_warehouses }}</div>
            <div class="stats-label">
                <i class="fas fa-warehouse me-2"></i>المستودعات
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card info">
            <div class="stats-number">{{ stats.total_categories }}</div>
            <div class="stats-label">
                <i class="fas fa-tags me-2"></i>التصنيفات
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card warning">
            <div class="stats-number">{{ stats.low_stock_products }}</div>
            <div class="stats-label">
                <i class="fas fa-exclamation-triangle me-2"></i>مخزون منخفض
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('add_warehouse') }}" class="btn btn-primary w-100 py-3">
                            <i class="fas fa-plus-circle fa-2x d-block mb-2"></i>
                            إضافة مستودع جديد
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('add_category') }}" class="btn btn-success w-100 py-3">
                            <i class="fas fa-tag fa-2x d-block mb-2"></i>
                            إضافة تصنيف جديد
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" onclick="showComingSoon()" class="btn btn-warning w-100 py-3">
                            <i class="fas fa-box fa-2x d-block mb-2"></i>
                            إضافة منتج جديد
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" onclick="showComingSoon()" class="btn btn-info w-100 py-3">
                            <i class="fas fa-file-invoice fa-2x d-block mb-2"></i>
                            إنشاء فاتورة جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الميزات الرئيسية -->
<div class="row mt-4">
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-warehouse me-2"></i>إدارة المستودعات
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">إدارة شاملة للمستودعات مع إمكانية إضافة وتعديل وحذف المستودعات وعرض محتوياتها.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>إضافة مستودعات جديدة</li>
                    <li><i class="fas fa-check text-success me-2"></i>تعديل بيانات المستودعات</li>
                    <li><i class="fas fa-check text-success me-2"></i>عرض محتويات كل مستودع</li>
                    <li><i class="fas fa-check text-success me-2"></i>إحصائيات تفصيلية</li>
                </ul>
                <a href="{{ url_for('warehouses') }}" class="btn btn-primary">إدارة المستودعات</a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tags me-2"></i>إدارة التصنيفات
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">تنظيم المنتجات في تصنيفات مختلفة لسهولة البحث والإدارة.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>إنشاء تصنيفات متعددة</li>
                    <li><i class="fas fa-check text-success me-2"></i>تعديل التصنيفات الموجودة</li>
                    <li><i class="fas fa-check text-success me-2"></i>عرض المنتجات حسب التصنيف</li>
                    <li><i class="fas fa-check text-success me-2"></i>إحصائيات التصنيفات</li>
                </ul>
                <a href="{{ url_for('categories') }}" class="btn btn-success">إدارة التصنيفات</a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-balance-scale me-2"></i>وحدات القياس
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">إدارة وحدات القياس المختلفة للمنتجات مثل الكيلو والقطعة واللتر.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>وحدات قياس متنوعة</li>
                    <li><i class="fas fa-check text-success me-2"></i>رموز مختصرة للوحدات</li>
                    <li><i class="fas fa-check text-success me-2"></i>وصف تفصيلي لكل وحدة</li>
                    <li><i class="fas fa-check text-success me-2"></i>ربط المنتجات بالوحدات</li>
                </ul>
                <a href="{{ url_for('units') }}" class="btn btn-warning">إدارة الوحدات</a>
            </div>
        </div>
    </div>
</div>

<!-- الميزات القادمة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-rocket me-2"></i>الميزات القادمة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-box fa-3x text-primary mb-3"></i>
                            <h6>إدارة المنتجات</h6>
                            <p class="text-muted small">إضافة وإدارة المنتجات مع الباركود والصور</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-shopping-cart fa-3x text-success mb-3"></i>
                            <h6>طلبات الشراء</h6>
                            <p class="text-muted small">إدارة طلبات الشراء والاستلام</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-file-invoice fa-3x text-warning mb-3"></i>
                            <h6>الفواتير</h6>
                            <p class="text-muted small">فواتير الإدخال والإخراج مع الطباعة</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-chart-bar fa-3x text-info mb-3"></i>
                            <h6>التقارير</h6>
                            <p class="text-muted small">تقارير شاملة وإحصائيات تفصيلية</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات النظام -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-cog me-2"></i>المواصفات التقنية:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fab fa-python me-2 text-primary"></i>مبني بلغة Python</li>
                            <li><i class="fas fa-globe me-2 text-success"></i>واجهة ويب متجاوبة</li>
                            <li><i class="fas fa-database me-2 text-info"></i>قاعدة بيانات SQLite</li>
                            <li><i class="fas fa-moon me-2 text-warning"></i>دعم الوضع الداكن</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-star me-2"></i>الميزات الرئيسية:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-barcode me-2 text-primary"></i>دعم الباركود</li>
                            <li><i class="fas fa-print me-2 text-success"></i>طباعة مباشرة</li>
                            <li><i class="fas fa-wifi-slash me-2 text-info"></i>يعمل بدون إنترنت</li>
                            <li><i class="fas fa-backup me-2 text-warning"></i>نسخ احتياطي</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(function() {
        // يمكن إضافة AJAX لتحديث الإحصائيات هنا
    }, 30000);
    
    // تأثير الحركة للبطاقات
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in');
        });
    });
</script>
{% endblock %}