{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - نظام إدارة المستودعات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">لوحة التحكم الرئيسية</h1>
    <div class="text-muted">
        <i class="fas fa-calendar-alt me-2"></i>
        <span id="current-time"></span>
    </div>
</div>

<!-- بطاقات الإحصائيات -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #007bff, #0056b3);">
            <div class="stats-number">{{ total_products or 0 }}</div>
            <div class="stats-label">
                <i class="fas fa-box me-2"></i>
                إجمالي المنتجات
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745, #1e7e34);">
            <div class="stats-number">{{ total_warehouses or 0 }}</div>
            <div class="stats-label">
                <i class="fas fa-warehouse me-2"></i>
                المستودعات
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
            <div class="stats-number">{{ total_customers or 0 }}</div>
            <div class="stats-label">
                <i class="fas fa-users me-2"></i>
                العملاء
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #dc3545, #c82333);">
            <div class="stats-number">{{ total_suppliers or 0 }}</div>
            <div class="stats-label">
                <i class="fas fa-truck me-2"></i>
                الموردين
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('products') }}" class="btn btn-primary w-100 py-3">
                            <i class="fas fa-plus-circle fa-2x d-block mb-2"></i>
                            إضافة منتج جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('entry_invoices') }}" class="btn btn-success w-100 py-3">
                            <i class="fas fa-arrow-down fa-2x d-block mb-2"></i>
                            فاتورة إدخال
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('exit_invoices') }}" class="btn btn-warning w-100 py-3">
                            <i class="fas fa-arrow-up fa-2x d-block mb-2"></i>
                            فاتورة إخراج
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('purchase_orders') }}" class="btn btn-info w-100 py-3">
                            <i class="fas fa-shopping-cart fa-2x d-block mb-2"></i>
                            طلب شراء
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- آخر العمليات -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر فواتير الإدخال
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center py-3">لا توجد فواتير إدخال حديثة</p>
                <div class="text-center mt-3">
                    <a href="{{ url_for('entry_invoices') }}" class="btn btn-outline-primary btn-sm">
                        عرض جميع فواتير الإدخال
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر فواتير الإخراج
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center py-3">لا توجد فواتير إخراج حديثة</p>
                <div class="text-center mt-3">
                    <a href="{{ url_for('exit_invoices') }}" class="btn btn-outline-primary btn-sm">
                        عرض جميع فواتير الإخراج
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- المنتجات منخفضة المخزون -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                    تنبيهات المخزون
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p class="text-muted">جميع المنتجات في مستوى مخزون جيد</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث الوقت كل ثانية
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleDateString('ar-SA') + ' ' + now.toLocaleTimeString('ar-SA');
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// تحديث الوقت عند تحميل الصفحة وكل دقيقة
updateTime();
setInterval(updateTime, 60000);
</script>
{% endblock %}