{% extends "base.html" %}

{% block title %}طلبات الشراء{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-shopping-cart me-2"></i>طلبات الشراء</h2>
                <button type="button" class="btn btn-primary" onclick="addPurchaseOrder()">
                    <i class="fas fa-plus me-1"></i>إضافة طلب شراء جديد
                </button>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="البحث برقم الطلب أو المورد...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="pending">قيد الانتظار</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                <i class="fas fa-times me-1"></i>مسح الفلاتر
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-success w-100" onclick="exportOrders()">
                                <i class="fas fa-file-excel me-1"></i>تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول طلبات الشراء -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="50">#</th>
                                    <th class="sortable">رقم الطلب</th>
                                    <th class="sortable">تاريخ الطلب</th>
                                    <th class="sortable">المورد</th>
                                    <th>عدد البنود</th>
                                    <th class="sortable">الحالة</th>
                                    <th>مدير المستودع</th>
                                    <th width="250">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="ordersTableBody">
                                {% for order in orders %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <strong>{{ order.order_number }}</strong>
                                    </td>
                                    <td>
                                        {{ order.order_date.strftime('%Y-%m-%d') if order.order_date else '-' }}
                                    </td>
                                    <td>
                                        {% if order.supplier_ref %}
                                        <span class="text-primary">{{ order.supplier_ref.name }}</span>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ order.items|length }} بند</span>
                                    </td>
                                    <td>
                                        {% if order.status == 'pending' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                        {% elif order.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                        {% elif order.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ order.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ order.warehouse_manager or '-' }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                    onclick="viewOrder({{ order.id }})" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="printOrder({{ order.id }})" title="طباعة">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            {% if order.status == 'pending' %}
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="editOrder({{ order.id }})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="completeOrder({{ order.id }})" title="إكمال">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="cancelOrder({{ order.id }})" title="إلغاء">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-shopping-cart fa-3x mb-3 d-block"></i>
                                        لا توجد طلبات شراء مسجلة حتى الآن
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة/تعديل طلب الشراء -->
<div class="modal fade" id="orderModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderModalTitle">إضافة طلب شراء جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="orderForm" method="POST" action="{{ url_for('purchase_orders') }}">
                <div class="modal-body">
                    <input type="hidden" id="order_id" name="order_id">
                    
                    <!-- معلومات الطلب الأساسية -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="order_number" class="form-label">رقم الطلب *</label>
                                <input type="text" class="form-control" id="order_number" name="order_number" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="order_date" class="form-label">تاريخ الطلب *</label>
                                <input type="date" class="form-control" id="order_date" name="order_date" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="supplier_id" class="form-label">المورد *</label>
                                <select class="form-select" id="supplier_id" name="supplier_id" required>
                                    <option value="">اختر المورد</option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- بنود الطلب -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6>بنود الطلب</h6>
                            <button type="button" class="btn btn-sm btn-success" onclick="addOrderItem()">
                                <i class="fas fa-plus me-1"></i>إضافة بند
                            </button>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="itemsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40%">اسم المنتج</th>
                                        <th width="15%">الكمية</th>
                                        <th width="15%">الوحدة</th>
                                        <th width="25%">ملاحظات</th>
                                        <th width="5%">حذف</th>
                                    </tr>
                                </thead>
                                <tbody id="itemsTableBody">
                                    <!-- سيتم إضافة البنود هنا بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="warehouse_manager" class="form-label">مدير المستودع</label>
                                <input type="text" class="form-control" id="warehouse_manager" name="warehouse_manager">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_manager" class="form-label">مدير الشركة</label>
                                <input type="text" class="form-control" id="company_manager" name="company_manager">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>حفظ الطلب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let currentOrders = [];
let itemCounter = 0;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadOrders();
    setupSearch();
    setupFilters();
    
    // تعيين تاريخ اليوم كافتراضي
    document.getElementById('order_date').value = new Date().toISOString().split('T')[0];
});

// تحميل الطلبات
function loadOrders() {
    // سيتم تنفيذ هذا لاحقاً عند إضافة API
}

// إعداد البحث
function setupSearch() {
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('input', function() {
        filterOrders();
    });
}

// إعداد الفلاتر
function setupFilters() {
    const statusFilter = document.getElementById('statusFilter');
    statusFilter.addEventListener('change', function() {
        filterOrders();
    });
}

// فلترة الطلبات
function filterOrders() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    
    const rows = document.querySelectorAll('#ordersTableBody tr');
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const statusCell = row.querySelector('td:nth-child(6)');
        const status = statusCell ? statusCell.textContent.toLowerCase() : '';
        
        const matchesSearch = text.includes(searchTerm);
        const matchesStatus = !statusFilter || status.includes(statusFilter);
        
        row.style.display = (matchesSearch && matchesStatus) ? '' : 'none';
    });
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    filterOrders();
}

// إضافة طلب شراء جديد
function addPurchaseOrder() {
    document.getElementById('orderModalTitle').textContent = 'إضافة طلب شراء جديد';
    document.getElementById('orderForm').reset();
    document.getElementById('order_id').value = '';
    document.getElementById('order_date').value = new Date().toISOString().split('T')[0];
    
    // مسح جدول البنود
    document.getElementById('itemsTableBody').innerHTML = '';
    itemCounter = 0;
    
    // إضافة بند واحد افتراضي
    addOrderItem();
    
    new bootstrap.Modal(document.getElementById('orderModal')).show();
}

// إضافة بند جديد للطلب
function addOrderItem() {
    itemCounter++;
    const tbody = document.getElementById('itemsTableBody');
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>
            <input type="text" class="form-control" name="item_name_${itemCounter}" 
                   placeholder="اسم المنتج" required>
        </td>
        <td>
            <input type="number" class="form-control" name="item_quantity_${itemCounter}" 
                   placeholder="الكمية" step="0.01" min="0" required>
        </td>
        <td>
            <input type="text" class="form-control" name="item_unit_${itemCounter}" 
                   placeholder="الوحدة">
        </td>
        <td>
            <input type="text" class="form-control" name="item_notes_${itemCounter}" 
                   placeholder="ملاحظات">
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeOrderItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(row);
}

// حذف بند من الطلب
function removeOrderItem(button) {
    const row = button.closest('tr');
    row.remove();
}

// عرض تفاصيل الطلب
function viewOrder(orderId) {
    fetch(`/api/purchase_orders/${orderId}`)
        .then(response => response.json())
        .then(data => {
            // إنشاء نافذة منبثقة لعرض التفاصيل
            const modalHtml = `
                <div class="modal fade" id="viewOrderModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تفاصيل طلب الشراء - ${data.order_number}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>رقم الطلب:</strong> ${data.order_number}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>تاريخ الطلب:</strong> ${data.order_date || '-'}
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>المورد:</strong> ${data.supplier_name || '-'}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>الحالة:</strong>
                                        <span class="badge ${data.status === 'pending' ? 'bg-warning' :
                                                           data.status === 'completed' ? 'bg-success' : 'bg-danger'}">
                                            ${data.status === 'pending' ? 'قيد الانتظار' :
                                              data.status === 'completed' ? 'مكتمل' : 'ملغي'}
                                        </span>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>مدير المستودع:</strong> ${data.warehouse_manager || '-'}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>مدير الشركة:</strong> ${data.company_manager || '-'}
                                    </div>
                                </div>
                                ${data.notes ? `<div class="mb-3"><strong>ملاحظات:</strong> ${data.notes}</div>` : ''}

                                <h6 class="mt-4 mb-3">بنود الطلب:</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>اسم المنتج</th>
                                                <th>الكمية</th>
                                                <th>الوحدة</th>
                                                <th>ملاحظات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${data.items.map(item => `
                                                <tr>
                                                    <td>${item.product_name}</td>
                                                    <td>${item.quantity}</td>
                                                    <td>${item.unit || '-'}</td>
                                                    <td>${item.notes || '-'}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                <button type="button" class="btn btn-primary" onclick="printOrder(${orderId})">
                                    <i class="fas fa-print me-1"></i>طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إزالة النافذة السابقة إن وجدت
            const existingModal = document.getElementById('viewOrderModal');
            if (existingModal) {
                existingModal.remove();
            }

            // إضافة النافذة الجديدة
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            new bootstrap.Modal(document.getElementById('viewOrderModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحميل تفاصيل الطلب');
        });
}

// طباعة الطلب
function printOrder(orderId) {
    window.open(`/purchase_orders/${orderId}/print`, '_blank');
}

// تعديل الطلب
function editOrder(orderId) {
    fetch(`/api/purchase_orders/${orderId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('orderModalTitle').textContent = 'تعديل طلب الشراء';
            document.getElementById('order_id').value = data.id;
            document.getElementById('order_number').value = data.order_number;
            document.getElementById('order_date').value = data.order_date;
            document.getElementById('supplier_id').value = data.supplier_id;
            document.getElementById('warehouse_manager').value = data.warehouse_manager || '';
            document.getElementById('company_manager').value = data.company_manager || '';
            document.getElementById('notes').value = data.notes || '';

            // مسح جدول البنود
            document.getElementById('itemsTableBody').innerHTML = '';
            itemCounter = 0;

            // إضافة البنود الموجودة
            data.items.forEach(item => {
                itemCounter++;
                const tbody = document.getElementById('itemsTableBody');
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <input type="text" class="form-control" name="item_name_${itemCounter}"
                               value="${item.product_name}" placeholder="اسم المنتج" required>
                    </td>
                    <td>
                        <input type="number" class="form-control" name="item_quantity_${itemCounter}"
                               value="${item.quantity}" placeholder="الكمية" step="0.01" min="0" required>
                    </td>
                    <td>
                        <input type="text" class="form-control" name="item_unit_${itemCounter}"
                               value="${item.unit || ''}" placeholder="الوحدة">
                    </td>
                    <td>
                        <input type="text" class="form-control" name="item_notes_${itemCounter}"
                               value="${item.notes || ''}" placeholder="ملاحظات">
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeOrderItem(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // إضافة بند فارغ إضافي
            addOrderItem();

            new bootstrap.Modal(document.getElementById('orderModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحميل بيانات الطلب');
        });
}

// إكمال الطلب
function completeOrder(orderId) {
    if (confirm('هل أنت متأكد من إكمال هذا الطلب؟')) {
        fetch(`/api/purchase_orders/${orderId}/complete`, {
            method: 'POST'
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('حدث خطأ في إكمال الطلب');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في إكمال الطلب');
        });
    }
}

// إلغاء الطلب
function cancelOrder(orderId) {
    if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
        fetch(`/api/purchase_orders/${orderId}/cancel`, {
            method: 'POST'
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('حدث خطأ في إلغاء الطلب');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في إلغاء الطلب');
        });
    }
}

// تصدير الطلبات
function exportOrders() {
    // سيتم تنفيذ هذا لاحقاً
    alert('ميزة التصدير ستكون متاحة قريباً');
}
</script>
{% endblock %}
