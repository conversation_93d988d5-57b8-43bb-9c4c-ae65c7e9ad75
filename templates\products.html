{% extends "base.html" %}

{% block title %}إدارة المنتجات - نظام إدارة المستودعات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">إدارة المنتجات</h1>
    <div class="btn-group">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#productModal">
            <i class="fas fa-plus me-2"></i>
            إضافة منتج جديد
        </button>
        <button type="button" class="btn btn-info" onclick="scanBarcode()">
            <i class="fas fa-barcode me-2"></i>
            مسح الباركود
        </button>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" id="searchInput" placeholder="اسم المنتج أو الباركود...">
            </div>
            <div class="col-md-3">
                <label class="form-label">التصنيف</label>
                <select class="form-select" id="categoryFilter">
                    <option value="">جميع التصنيفات</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}">{{ category.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">المستودع</label>
                <select class="form-select" id="warehouseFilter">
                    <option value="">جميع المستودعات</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select class="form-select" id="conditionFilter">
                    <option value="">جميع الحالات</option>
                    {% for condition in conditions %}
                    <option value="{{ condition.id }}">{{ condition.name }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
    </div>
</div>

<!-- جدول المنتجات -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">
                    <i class="fas fa-boxes me-2"></i>
                    قائمة المنتجات
                </h5>
            </div>
            <div class="col-auto">
                <span class="badge bg-primary">{{ products|length }} منتج</span>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if products %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th class="sortable">اسم المنتج</th>
                        <th class="sortable">الباركود</th>
                        <th class="sortable">التصنيف</th>
                        <th class="sortable">الوحدة</th>
                        <th class="sortable">الحالة</th>
                        <th class="sortable">الكمية</th>
                        <th class="sortable">السعر</th>
                        <th width="200">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr>
                        <td>
                            {% if product.image_url %}
                            <img src="{{ product.image_url }}" alt="{{ product.name }}"
                                 class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                            {% else %}
                            <div class="bg-light d-flex align-items-center justify-content-center"
                                 style="width: 50px; height: 50px; border-radius: 4px;">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ product.name }}</strong>
                            {% if product.description %}
                            <br><small class="text-muted">{{ product.description[:50] }}...</small>
                            {% endif %}
                        </td>
                        <td>
                            <code>{{ product.barcode or '-' }}</code>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ product.category.name if product.category else '-' }}</span>
                        </td>
                        <td>{{ product.unit.name if product.unit else '-' }}</td>
                        <td>
                            <span class="badge bg-info">{{ product.condition.name if product.condition else '-' }}</span>
                        </td>
                        <td>
                            <span class="badge {% if product.quantity <= product.min_quantity %}bg-danger{% else %}bg-success{% endif %}">
                                {{ product.quantity }}
                            </span>
                        </td>
                        <td>{{ "%.2f"|format(product.price) }} ريال</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-info"
                                        onclick="viewProduct({{ product.id }})"
                                        title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-warning"
                                        onclick="editProduct({{ product.id }})"
                                        title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-success"
                                        onclick="printBarcode({{ product.id }})"
                                        title="طباعة الباركود">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger delete-btn"
                                        onclick="deleteProduct({{ product.id }})"
                                        data-item-name="{{ product.name }}"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد منتجات</h5>
            <p class="text-muted">ابدأ بإضافة منتج جديد</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#productModal">
                <i class="fas fa-plus me-2"></i>
                إضافة منتج جديد
            </button>
        </div>
        {% endif %}
    </div>
</div>
<!-- نافذة إضافة/تعديل المنتج -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productModalTitle">إضافة منتج جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="productForm" method="POST" action="{{ url_for('products') }}" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="product_id" name="product_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="product_name" class="form-label">اسم المنتج *</label>
                                <input type="text" class="form-control" id="product_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="product_barcode" class="form-label">الباركود</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="product_barcode" name="barcode">
                                    <button type="button" class="btn btn-outline-secondary" onclick="generateBarcode()">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="product_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="product_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="product_category" class="form-label">التصنيف *</label>
                                <select class="form-select" id="product_category" name="category_id" required>
                                    <option value="">اختر التصنيف</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="product_unit" class="form-label">الوحدة *</label>
                                <select class="form-select" id="product_unit" name="unit_id" required>
                                    <option value="">اختر الوحدة</option>
                                    {% for unit in units %}
                                    <option value="{{ unit.id }}">{{ unit.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="product_condition" class="form-label">الحالة *</label>
                                <select class="form-select" id="product_condition" name="condition_id" required>
                                    <option value="">اختر الحالة</option>
                                    {% for condition in conditions %}
                                    <option value="{{ condition.id }}">{{ condition.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="product_quantity" class="form-label">الكمية *</label>
                                <input type="number" class="form-control" id="product_quantity" name="quantity" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="product_min_quantity" class="form-label">الحد الأدنى</label>
                                <input type="number" class="form-control" id="product_min_quantity" name="min_quantity" min="0">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="product_price" class="form-label">السعر *</label>
                                <input type="number" class="form-control" id="product_price" name="price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="product_warehouse" class="form-label">المستودع *</label>
                                <select class="form-select" id="product_warehouse" name="warehouse_id" required>
                                    <option value="">اختر المستودع</option>
                                    {% for warehouse in warehouses %}
                                    <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="product_image" class="form-label">صورة المنتج</label>
                        <input type="file" class="form-control" id="product_image" name="image" accept="image/*">
                        <div class="form-text">يُفضل صور بحجم 500x500 بكسل</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentProductId = null;

function addProduct() {
    currentProductId = null;
    document.getElementById('productModalTitle').textContent = 'إضافة منتج جديد';
    document.getElementById('productForm').reset();
    document.getElementById('product_id').value = '';
}

function editProduct(id) {
    currentProductId = id;
    document.getElementById('productModalTitle').textContent = 'تعديل المنتج';

    fetch(`/api/products/${id}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('product_id').value = data.id;
            document.getElementById('product_name').value = data.name;
            document.getElementById('product_barcode').value = data.barcode || '';
            document.getElementById('product_description').value = data.description || '';
            document.getElementById('product_category').value = data.category_id || '';
            document.getElementById('product_unit').value = data.unit_id || '';
            document.getElementById('product_condition').value = data.condition_id || '';
            document.getElementById('product_quantity').value = data.quantity;
            document.getElementById('product_min_quantity').value = data.min_quantity || '';
            document.getElementById('product_price').value = data.price;
            document.getElementById('product_warehouse').value = data.warehouse_id || '';

            new bootstrap.Modal(document.getElementById('productModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في جلب بيانات المنتج');
        });
}

function viewProduct(id) {
    alert('سيتم تطوير هذه الميزة قريباً');
}

function deleteProduct(id) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        fetch(`/api/products/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

function generateBarcode() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    const barcode = `${timestamp}${random}`.slice(-13);
    document.getElementById('product_barcode').value = barcode;
}

function scanBarcode() {
    alert('سيتم تطوير ميزة مسح الباركود قريباً');
}

function printBarcode(id) {
    alert('سيتم تطوير ميزة طباعة الباركود قريباً');
}

document.getElementById('productModal').addEventListener('show.bs.modal', function (event) {
    if (!currentProductId) {
        addProduct();
    }
});
</script>
{% endblock %}